@import "../../theme/sass/auth";
@import "../../../styles";

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
  z-index: 100000000 !important;

  .customer-dd .c-btn .c-remove.clear-all {
    position: relative;
    height: 10px;
    width: 10px;
    margin-left: 50px;
    margin-top: 2px;
  }

  .customer-dd .dropdown-list {
    top: 52px !important;
    position: absolute !important;
    left: 0 !important;
  }

  .customer-dd .c-btn{
    padding: 12px 15px;
    border: 2px solid #222222 !important;
  }



  .modal-content.target-modal {
    .child-rfc {
      margin-top: 10px !important;
      font-size: 18px;
      color: #222222;
      font-weight: 400;
    }

    .input-field-company {
      width: 80%;
    }

    .upload-box button {
      padding: 14px 0 12px 0;
      width: 200px;
      border-radius: 6px;
      background-color: transparent;
      border: 1px solid #707070;
    }

    .delete-btn {
      background: none;
      border: 1px solid hsl(23, 100%, 60%);
      border-radius: 4px;
      color: #ff8033;
      cursor: pointer;
      margin-top: 46px;
      gap: 10px;
      // margin: 39px 5px 5px 5px;
      padding: 10px 16px 10px 16px;

      &:hover {
        background-color: #fff0e8;
      }

    }

    .delete-btn i {
      font-size: 1em;
    }

    .filename-text {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 98%;
      display: block;
    }

    .btn-upload {
      margin-top: 5px;
      padding: 5px 10px;
      cursor: pointer;
      font-family: $sans-font-family;
    }

    /* Uploaded files container */
    .uploaded-files {
      padding: 10px;
      border: 2px solid #ff6f1f;
      border-radius: 7px;
      min-height: 150px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    overflow: auto;
    background-color: $white-bg;
    border-radius: 8px;
    max-width: 756px;
    max-height: 858px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    zoom: 0.7;

    // Increase width for edit mode to accommodate download buttons
    &.edit-mode {
      max-width: 800px;
    }

    .compulsory-field {
      font-size: 18px;
      font-weight: 400;
      color: #F32E1E;
    }

    .compulsory-bold-field {
      font-size: 20px;
      font-weight: 400;
      color: #F32E1E;
    }

    .modal-upper-section {
      padding: 30px 30px 0 50px;
    }

    .modal-middle-section {
      background-color: #FFF1E9;
      padding: 15px 50px;
    }

    .modal-bottom-section {
      padding: 20px 50px 0 50px;
      margin-bottom: 40px;
    }


    .modal-heading {
      font-size: 30px;
      font-weight: 600;
      line-height: 100%;
      color: #222222;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 600;
      line-height: 100%;
      color: #222222;
      margin: 10px 0;
    }

    .form-section {
      .section-header {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        border-bottom: 1px solid #ddd;
      }

      &.grower-section {
        margin: 25px 0;

        .checkbox-container {
          display: flex;
          align-items: center;
          gap: 12px;
          width: max-content;
          position: relative;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          cursor: pointer;

          input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;

            &:checked~.checkmark {
              background-color: #FF8033;
              border: none;
            }

            &:checked~.checkmark:after {
              display: block;
            }
          }

          .checkmark {
            height: 20px;
            width: 20px;
            border: 3px solid #1C1B1F;
            border-radius: 3px;

            &:after {
              content: "";
              position: absolute;
              display: none;
              left: 6px;
              top: 2px;
              width: 8px;
              height: 12px;
              border: 3px solid white;
              border-width: 0 3px 3px 0;
              -webkit-transform: rotate(45deg);
              -ms-transform: rotate(45deg);
              transform: rotate(45deg);
            }
          }

          .grower-text {
            font-size: 16px;
            font-weight: 500;
            line-height: 100%;
            color: #222222;
          }
        }
      }

      &.input-grid {
        display: flex;
        gap: 20px;

        .form-group {

          .error-message {
            color: #f44336;
            font-size: 13px;
            margin-top: 5px;
          }

          .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            line-height: 1.4;
          }

          input.invalid {
            border-color: #f44336;
          }

          .compulsory-field,
          .required {
            color: #f44336;
            margin-left: 3px;
          }

          display: flex;
          flex-direction: column;

          label {
            font-size: 18px;
            color: #222222;
            font-weight: 400;
          }

          input[type="text"] {
            padding: 10px;
            border: 1px solid #1A3661;
            border-radius: 4px;
            font-size: 18px;
            font-weight: 500;
            color: #333;
            width: 286px;
          }
        }
      }

      &.set-target-section {
        .target-values {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-top: 28px;

          .target-item {
            display: flex;
            align-items: center;
            gap: 1rem;

            label {
              width: 160px;
              font-weight: 500;
              font-size: 16px;
              display: inline-block;
              color: #222222;
            }

            input[type="text"],
            input[type="number"] {
              padding: 0.4rem 0.6rem;
              border: 1px solid #1A3661;
              border-radius: 4px;
              width: 110px;
              color: #333;
            }

            .percentage-input {
              display: flex;
              align-items: center;
              gap: 0.2rem;
              color: #333;
            }

            .amount-display {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-left: 15px;

              span {
                font-size: 16px;
                font-weight: 400;
                color: #333;
              }

              input[type="text"] {
                width: 80px;
                color: #333;
              }
            }
          }
        }
      }

      &.target-duration-section {
        .date-inputs {
          display: flex;
          gap: 20px;
          margin-top: 16px;

          .date-group {
            display: flex;
            flex-direction: column;
            gap: 8px;

            label {
              font-size: 16px;
              color: #000000;
              font-weight: 500;
              line-height: 100%;
              letter-spacing: 0%;
            }

            .date-picker {
              position: relative;

              input {
                padding: 10px;
                border: 1px solid #1A3661;
                border-radius: 4px;
                font-size: 14px;
                color: #333;
                width: 286px;
                cursor: pointer;
              }

              mat-datepicker-toggle {
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                color: #FF6B00;
              }
            }
          }
        }
      }
    }

    .button-group {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin-top: 50px;
      margin-right: 40px;

      .btn {
        width: 200px;
        margin-top: 10px;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &.cancel {
          color: #FF8033;
          border: 1px solid #FF8033;
        }

        &.add {
          background-color: #FF8033;
          color: white;

          &:hover {
            background-color: #e67147;
          }
        }
      }
    }
  }
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
.form-group {
  position: relative;
  input#rootRfcId {
    letter-spacing: 0.5px;

    &::placeholder {
      text-transform: none;
      color: #999;
    }
  }

  .error-message {
    color: #f44336;
    font-size: 12px;
    margin-top: 5px;
    font-weight: 500;
    display: flex;
    align-items: center;

    &:before {
      margin-right: 5px;
      font-size: 14px;
    }
  }

  .help-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
    background-color: #f8f8f8;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #FF8033;

    br+br+span {
      font-weight: 500;
      color: #FF8033;
    }
  }

  input.invalid {
    border-color: #f44336 !important;
    background-color: #fff9f9;

    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }

  .compulsory-field,
  .required {
    color: #f44336;
    margin-left: 3px;
    font-weight: 600;
  }
}

/* Add these styles to your component's CSS file */
.form-group-child-rfc {
  margin-bottom: 1rem;
  padding-top: 1rem;
  // padding: 1rem;
  // background: #f8f9fa;
  border-radius: 4px;
}

.child-rfc-row {
  display: flex;
  gap: 1rem;
  align-items: center; // Change to center to align all items including buttons
}


.upload-container-image {
  height: 50px;
  flex: 1;
}

.delete-container {
  display: flex;
  gap: 12px;
  align-items: center;
  align-self: center;
  margin-top: 0;
  min-height: 40px;

  .download-btn {
    background: #FF8033 !important;
    border: 1px solid #FF8033 !important;
    border-radius: 6px !important;
    color: white !important;
    cursor: pointer !important;
    padding: 10px !important;
    transition: all 0.2s !important;
    display: flex !important;
    margin-top: 7px;
    align-items: center !important;
    justify-content: center !important;
    width: 48px !important;
    height: 48px !important;
    position: relative !important;

    &:hover {
      background-color: #e6722d;
      border-color: #e6722d;
    }

    .download-icon {
      width: 26px !important;
      height: 26px !important;
      object-fit: contain !important;
      display: block !important;
      opacity: 1 !important;
      visibility: visible !important;
      z-index: 1 !important;
      position: relative !important;
      // For SVG files, use fill instead of filter
      fill: white !important;
      // Fallback filter for PNG files
      filter: brightness(0) invert(1) !important;
    }

    &::before {
      content: "⬇";
      font-size: 18px;
      color: white;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 0;
    }

    // Hide fallback arrow if image loads
    &:has(.download-icon[src]):not(:has(.download-icon[src=""]))::before {
      display: none;
    }
  }

  // .delete-btn {
  //   background: none !important;
  //   border: 1px solid #FF8033 !important;
  //   border-radius: 6px !important;
  //   color: #FF8033 !important;
  //   cursor: pointer !important;
  //   padding: 10px !important;
  //   transition: all 0.2s !important;
  //   display: flex !important;
  //   align-items: center !important;
  //   justify-content: center !important;
  //   width: 48px !important;
  //   height: 48px !important;
  //   position: relative !important;

  //   &:hover:not(:disabled) {
  //     background-color: #fff0e8 !important;
  //   }

  //   &:disabled {
  //     opacity: 0.5 !important;
  //     cursor: not-allowed !important;
  //   }

  //   i {
  //     font-size: 26px !important;
  //     line-height: 1 !important;
  //     display: flex !important;
  //     align-items: center !important;
  //     justify-content: center !important;
  //   }
  // }

  // // When only delete button is present (add mode), center it properly
  // &:has(.delete-btn:only-child) {
  //   justify-content: center;
  //   gap: 0;
  // }

  // // Fallback for browsers that don't support :has()
  // .delete-btn:only-child {
  //   margin: 0 auto;
  // }
}



.input-field {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

/* File input styling */
.file-input-wrapper {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 0.1px;
  height: 0.1px;
}

.file-input-label {
  display: block;
  width: 100%;
  cursor: pointer;
}

.file-input-text {
  display: block;
  padding: 0.8rem;
  border: 1px solid #1A3661;
  border-radius: 4px;
  background: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-input:hover+.file-input-text,
.file-input:focus+.file-input-text {
  border-color: #222222;
}

.download-btn {
  background: #FF8033;
  border: 1px solid #FF8033;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding: 8px 12px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #e6722d;
    border-color: #e6722d;
  }

  i {
    font-size: 14px;
  }
}

// .delete-btn {
//   background: #dc3545;
//   border: 1px solid #dc3545;
//   border-radius: 4px;
//   color: white;
//   cursor: pointer;
//   font-size: 1rem;
//   padding: 8px 12px;
//   transition: all 0.2s;
//   display: inline-flex;
//   align-items: center;
//   justify-content: center;

//   &:hover:not(:disabled) {
//     background-color: #c82333;
//     border-color: #c82333;
//   }

//   &:disabled {
//     opacity: 0.5;
//     cursor: not-allowed;
//   }

//   i {
//     font-size: 14px;
//   }
// }

.error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.compulsory-field {
  color: #dc3545;
}

.child-rfc-row {
  display: flex;
  gap: 1rem;
  align-items: center; // Change to center to align all items including buttons
}


.additional-height {
  height: 100px;
}

.upload-container-image {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// .delete-container {
//   flex: 0 0 auto;
//   align-self: flex-end;
//   // padding-top: 1.5rem;
// }

.input-field {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #1A3661;
  border-radius: 4px;
}

.upload-box {
  display: flex;
  flex-direction: column;
}

.btn-upload {
  padding: 0.5rem;
  background: #f0f0f0;
  border: 1px solid #1A3661;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filename-text {
  background: #e8f4fd;
  border-color: #c2e0ff;
}

// .delete-btn {
//   position: relative;
//   top: -20px;
//   background: none;
//   border: none;
//   color: #dc3545;
//   cursor: pointer;
//   font-size: 1.2rem;
// }

.tax-proof {
  margin-top: -4px;
}

.error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.compulsory-field {
  color: #dc3545;
}

/* For long filenames */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  display: inline-block;
}

// Add this to make the input group more compact
.input-grid .form-group {
  margin-bottom: 5px; // Reduce space between form groups
}

// Responsive adjustments
@media (max-width: 768px) {
  .input-grid {
    flex-direction: column;

    .form-group {
      width: 100% !important;

      input[type="text"] {
        width: 100% !important;
      }
    }
  }

  .help-text {
    font-size: 11px;
  }
}


// Error message styling
.error-message {
  color: #f44336;
  font-size: 12px;
  margin-top: 5px;
  font-weight: 500;
  display: flex;
  align-items: center;

  &:before {
    margin-right: 5px;
    font-size: 14px;
  }
}

::ng-deep .mat-calendar-body-selected .mat-calendar-body-cell-content {
  background-color: #FF8033 !important;
  color: white !important;
  border-radius: 50% !important;
}

::ng-deep .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover .mat-calendar-body-cell-content {
  background-color: #FF8033 !important;
  color: white !important;
}

::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected) .mat-calendar-body-cell-content {
  border: none !important;
}

::ng-deep .mat-calendar-body-selected .mat-calendar-body-cell-content,
::ng-deep .mat-calendar-body-range-start .mat-calendar-body-cell-content,
::ng-deep .mat-calendar-body-range-end .mat-calendar-body-cell-content {
  background-color: #FF8033 !important;
  color: white !important;
  border-radius: 50% !important;
}

::ng-deep .mat-calendar-body-selected {
  background-color: #FF8033 !important;
}

.add-btn-container {
  display: flex;
  height: auto;
  margin: 10px;

  .addSchemeButton {
    height: 37px;
    background-color: #ff8033;
    border-radius: 0.25rem;
    border: 1px solid #ff8033;
    color: #fff;
    padding: 0px 14px 0 14px;
    width: 30%;
  }

  .addSchemeButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.form-group-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;
  font-size: 14px;
  width: 100%;

  span {
    width: 96%;
    font-size: 14px;
    label {
      font-size: 18px;
      color: #222222;
      font-weight: 400;
    }

    .input-field {
      width: 100%;
    }

    ::ng-deep .angular2-multiselect {
      width: 100%;
    }
  }
}

.form-group-row input:focus {
  border: none;
}


/* Custom styles for the dropdown in the target modal */
app-custom-dropdown {
  width: 100%;
  display: block;

  ::ng-deep .custom-dropdown {
    .selected-option {
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #ced4da;
      height: 38px;
      font-size: 14px;
    }

    .options-container {
      border-radius: 0 0 4px 4px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      /* Ensure dropdown appears above other elements */
    }

    .option {
      font-size: 14px;
    }

    .search-box input {
      height: 36px;
    }
  }
}

/* Adjust form group row for the dropdown */
.form-group-row {
  margin-bottom: 15px;

  span {
    display: block;
    width: 100%;
    font-size: 14px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;

      .required {
        color: #ff0000;
        margin-left: 4px;
      }
    }
  }
}

// Make sure the dropdown is visible and properly styled
.form-group-row {
  span {
    width: 100%;
    font-size: 16px;
    label {
      display: block;
      margin-bottom: 7px;
      font-size: 18px;
      color: #222222;
      font-weight: 400;
    }
  }
}

// Make sure any overlays in this component don't block toasts
.modal-overlay {
  z-index: 1000 !important;
}

// Ensure any fixed position elements don't interfere with toasts
.modal-content {
  position: relative;
  z-index: 1000;
}

.disabled-leader-display {
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
  cursor: not-allowed;
  color: #555;
  padding: 12px;
  position: relative;
  top: -3px;
}