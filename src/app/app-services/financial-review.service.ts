import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EventEmitter, Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { throwError } from 'rxjs';
import { Utility } from "../shared/utility/utility";

@Injectable({
  providedIn: 'root'
})
export class FinanceReviewService {
  _openedPopup: EventEmitter<any> = new EventEmitter(true);
  baseUrl = environment.baseUrl;
  addSchemeURL: any = 'group-slab-brand-mappings';
  updateSchemeURL: any = '/reward/update/';
  getAllSchemeURL: any = '/reward/all';
  getSpecificRewardDetailsURL: any = '/reward/byId/';
  getAllFinanceReviewURL: any = 'reward/finance-reviews/all';
  getAllFinanceReviewProgressURL: any = 'financial-review/bonification-exceptions'; 
  updateFinanceReviewConfigURL: any = 'update-financial-review-configurations'; 
  getHistoryURL: any = 'financial-review-configuration-history';  
  getFinanceConfigURL: any = 'get-financial-review-config-data';
  addConfiguration: string = 'add-financial-review-configurations'
  getFinanceReviewByIdURL: any = 'finance-review/';
  getAllExportRedeemedSchemesURL: any = 'customer-redeem-history/byCustomerType';
  getAllScannedSchemesURL: any = 'scannedproductdetail/history/byCustomerType';
  getAllScannedCategoryURL: any = 'scanning-categories/by-scan-category/';
  getAllRedeemedMethodURL: any = 'customer-redeem-history/redeem-methods';
  getAllFinanceReviewStatusMethodURL: any = 'get-bonification-status';
  getAllExportScannedSchemesURL: any = '/scannedproductdetail/export';
  getAllExportSchemesURL: any = 'reward/export';
  deleteSchemeURL: any = 'reward/delete';
  getFieldType: any = 'FORAGES';
  displayData: any = '';
  menuItems: any = '';
  invoiceStatus: string = 'get-invoice-status'
  _isActive: EventEmitter<any> = new EventEmitter(true);
  _ScanReward: EventEmitter<any> = new EventEmitter(true);
  _scannedRewardData: EventEmitter<any> = new EventEmitter(true);
  _scannedRewardDataPage: EventEmitter<any> = new EventEmitter(true);
  _tableDialogBox: EventEmitter<any> = new EventEmitter(true);
  _closedPopup: EventEmitter<any> = new EventEmitter(true);
  _rewardSlabHeading: EventEmitter<any> = new EventEmitter(true);
  _rewardSlabData: EventEmitter<any> = new EventEmitter(true);
  _disabledSidebar: EventEmitter<any> = new EventEmitter(true);
  _sidebarPin: EventEmitter<any> = new EventEmitter(true);
  _openDialog: EventEmitter<any> = new EventEmitter(true);
  _displayTableData: EventEmitter<any> = new EventEmitter(true);
  _seasonId: EventEmitter<any> = new EventEmitter(true);
  _menuItem: EventEmitter<any> = new EventEmitter(true);
  showClick: any;
  private invoiceEditSubject = new Subject<boolean>();
  invoiceEdit$ = this.invoiceEditSubject.asObservable();
  constructor(private http: HttpClient, private utility: Utility) {}

  setDialogData(data: any) {
    this.displayData = data;
  }
  getDialogValue() {
    return this.displayData;
  }

  getAllLastAddedSeason(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'group-slab-brand-mappings/previous/by-scan-category-id/' +
        data.scanCategoryId +
        '?scanningCategoryId=' +
        data.scanningCategoryId +
        '&cropId=' +
        data.cropId,
      { headers: headers, responseType: 'text' }
    );
  }

  addFinanceReview(requestPayload: any) {
    const headers = this.authorizationKey() || {};
    const missingFiles = requestPayload.childRfcs?.filter((child: any) => !child.taxProof);
    if (missingFiles?.length) {
      throw new Error('Each child RFC must have a corresponding childRfcFile.');
    }
    const childRfcs = requestPayload.childRfcs?.map((child: any) => ({
      rfc: child.rfc,
      companyName: child.companyName
    })) || [];

    const transformedPayload = {
      amount: Number(requestPayload.totalTarget),
      startDate: requestPayload.startDate,
      endDate: requestPayload.endDate,
      grower: requestPayload.grower,
      cropProtectionAmount: requestPayload.cpAmount,
      nppAmount: requestPayload.nppAmount,
      childRfcs: childRfcs
    };
    const encryptedObj = this.utility.encryptString(transformedPayload);
    if (!encryptedObj?.encryptedBody) {
      throw new Error('Encryption failed');
    }
    const encryptedPayload = encryptedObj.encryptedBody;
    const formData = new FormData();
    formData.append('financeReviewDTO', encryptedPayload);
    const childRfcFiles = requestPayload.childRfcs?.map((child: any) => child.taxProof);
    if (childRfcFiles?.length) {
      childRfcFiles.forEach((file: File) => {
        if (file instanceof File) {
          formData.append('childRfcFiles', file);
        }
      });
    }
    const url = `${this.baseUrl}assign/reward/finance-review?leaderId=${encodeURIComponent(requestPayload.leaderId)}&rootRfc=${encodeURIComponent(requestPayload.rootRfcId)}`;
    return this.http.post(url, formData, {
      headers,
      responseType: 'text'
    });
  }
  
  editFinanceReview(requestPayload: any) {
    const headers = this.authorizationKey() || {};  
    const missingFiles = requestPayload.childRfcs?.filter((child: any) => !child.taxProof);
    if (missingFiles?.length) {
      throw new Error('Each child RFC must have a corresponding childRfcFile.');
    }
    const childRfcs = requestPayload.childRfcs?.map((child: any) => ({
      rfc: child.rfc,
      companyName: child.companyName
    })) || [];
    const transformedPayload = {
      amount: Number(requestPayload.totalTarget) || 0,
      startDate: requestPayload.startDate,
      endDate: requestPayload.endDate,
      grower: requestPayload.grower || false,
      cropProtectionAmount: requestPayload.cpAmount || 0,
      nppAmount: requestPayload.nppAmount || 0,
      childRfcs: childRfcs
    };
    const encryptedObj = this.utility.encryptString(transformedPayload);
    if (!encryptedObj?.encryptedBody) {
      throw new Error('Encryption failed');
    }
  
    const encryptedPayload = encryptedObj.encryptedBody;
    const formData = new FormData();
    formData.append('financeReviewDTO', encryptedPayload);
    const childRfcFiles = requestPayload.childRfcs?.map((child: any) => child.taxProof);
    if (childRfcFiles?.length) {
      childRfcFiles.forEach((file: File) => {
        if (file instanceof File) {
          formData.append('childRfcFiles', file);
        }
      });
    }
    const url = `${this.baseUrl}update/reward/finance-review?id=${encodeURIComponent(requestPayload.id)}&rootRfc=${encodeURIComponent(requestPayload.rootRfcId)}`;
    return this.http.post(url, formData, {
      headers,
      responseType: 'text'
    });
  }

  getInvoiceStatus() {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + this.invoiceStatus,
      { headers: headers, responseType: 'text' }
    );
  }

  approveFinanceReview(data: any) {
    const headers = this.authorizationKey();
    
    const id = data.financeReviewId || data.leaderId || data.id;
    
    if (!id) {
      return throwError(() => new Error('Missing finance review ID'));
    }
    
    return this.http.post(
      `${this.baseUrl}approve-finance-review-request?financeReviewId=${id}`,
      '',
      { headers: headers, responseType: 'text' }
    );
  }

  getAllEarnedRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards' +
        '?startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&customerTypeId=' +
        data.customerType +
        '&page=' +
        data.currentPage +
        '&size=' +
        data.size +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        data.scanCategoryId +
        '&unPaged=' +
        data.unPaged +
        '&regionIds=' +
        data.regionIds +
        '&territoryIds=' +
        data.territoryIds,
      { headers: headers, responseType: 'text' }
    );
  }

  exportAllEarnedRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/sku-wise/export' +
        '?startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&customerTypeId=' +
        data.customerType +
        '&size=' +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        1 +
        '&unPaged=' +
        data.unPaged +
        '&regionIds=' +
        data.regionIds +
        '&territoryIds=' +
        data.territoryIds,
      { responseType: 'blob' as 'json', headers: headers }
    );
  }

  exportAllEarnedRewardForagesData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/sku-wise/export' +
        '?startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&customerTypeId=' +
        data.customerType +
        '&size=' +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        2 +
        '&unPaged=' +
        data.unPaged +
        '&regionIds=' +
        data.regionIds +
        '&territoryIds=' +
        data.territoryIds,
      { responseType: 'blob' as 'json', headers: headers }
    );
  }

  getAllRetailerData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'earned-rewards' +
        '?startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&customerTypeId=' +
        data.customerType +
        '&page=' +
        data.currentPage +
        '&size=' +
        data.size +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        1,
      { headers: headers, responseType: 'text' }
    );
  }

  getCropDataFC() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + 'crops/by-scan-category/1', {
      headers: headers,
      responseType: 'text',
    });
  }

  getCropDataForages() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + 'crops/by-scan-category/2', {
      headers: headers,
      responseType: 'text',
    });
  }

  getSlabSeasonData() {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'scanning-categories/by-scan-category/1',
      { headers: headers, responseType: 'text' }
    );
  }

  getForagesSlabSeasonData() {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'scanning-categories/by-scan-category/2',
      { headers: headers, responseType: 'text' }
    );
  }

  getSlabSkuDataByCropId(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'brands/by-crop/' +
        data.cropId +
        '?brandIds=' +
        data.brandId,
      { headers: headers, responseType: 'text' }
    );
  }

  getAllSlabSkuDataByCropId(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'brands/by-crop/' + data.cropId + '?brandIds=',
      { headers: headers, responseType: 'text' }
    );
  }

  exportAllRetailerData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'earned-rewards/export' +
        '?startDate=' +
        data.startDate +
        '&endDate=' +
        data.endDate +
        '&customerTypeId=' +
        data.customerType +
        '&size=' +
        data.size +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        1,
      { responseType: 'blob' as 'json', headers: headers }
    );
  }

  getScannedRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/scanning-history' +
        '?customerId=' +
        data.id +
        '&size=' +
        data.size +
        '&page=' +
        data.page +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        1,
      { headers: headers, responseType: 'text' }
    );
  }

  getForagesScannedRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/scanning-history' +
        '?customerId=' +
        data.id +
        '&size=' +
        data.size +
        '&page=' +
        data.page +
        '&searchedValue=' +
        data.searchedValue +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        2,
      { headers: headers, responseType: 'text' }
    );
  }

  exportRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/scanning-history/export' +
        '?customerId=' +
        data.id +
        '&firmName=' +
        data.firmName +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        1,
      { responseType: 'blob' as 'json', headers: headers }
    );
  }

  exportForagesRewardData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'loyalty-rewards/scanning-history/export' +
        '?customerId=' +
        data.id +
        '&firmName=' +
        data.firmName +
        '&scanningCategoryId=' +
        data.scanningCategoryId +
        '&scanCategoryId=' +
        2,
      { responseType: 'blob' as 'json', headers: headers }
    );
  }

  addScheme(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + this.addSchemeURL, body, {
      headers: headers,
      responseType: 'text',
    });
  }

  addSlabIncentive(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + 'incentive', body, {
      headers: headers,
      responseType: 'text',
    });
  }

  updateScheme(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseUrl + this.updateSchemeURL + body.rewardId,
      body,
      { headers: headers, responseType: 'text' }
    );
  }

  getShowClick() {
    return this.showClick;
  }

  setShowClick(data: any) {
    this.showClick = data;
  }

  getAllRewardSlabData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'product-slab/all?cropName=' + data.CropName,
      { headers: headers }
    );
  }

  getRewardSlabData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'group-slab-brand-mappings/by-scan-category-id/' +
        data.scanCategoryId +
        '?cropId=' +
        data.cropId +
        '&scanningCategoryId=' +
        data.scanningCategoryId,
      { headers: headers, responseType: 'text' }
    );
  }

  exportAllRewardSlabData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'group-slab/all?cropName=' + data.CropName,
      { headers: headers, responseType: 'text' }
    );
  }

  getAllFCRewardSlabSeasonDropdownData() {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'scanning-categories/by-scan-category/1',
      { headers: headers, responseType: 'text' }
    );
  }
  getFCrewardSlabCropTypeData() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + 'crops/by-scan-category/1', {
      headers: headers,
      responseType: 'text',
    });
  }

  getForagesrewardSlabCropTypeData() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + 'crops/by-scan-category/2', {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllForagesRewardSlabSeasonDropdownData() {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl + 'scanning-categories/by-scan-category/2',
      { headers: headers, responseType: 'text' }
    );
  }

  getAllRegionDropdwonData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'regions/drop-down/by-scan-category/' +
        data.scanningCategoryId,
      { headers: headers, responseType: 'text' }
    );
  }
  territoriesByRegionCodeURL: any =
  'territories/drop-down/by-scan-category/';
  getAllTerritoriesByRegionCode(data: any) {
    let URL =
      this.territoriesByRegionCodeURL +
      data.scanningCategoryId +
      '?regionIds=' +
      data.regionIds;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllTerritoryDropdwonData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseUrl +
        'territories/drop-down/by-scan-category/' +
        data.scanningCategoryId?+'regionIds='+data?.regionIds:'',
      { headers: headers, responseType: 'text' }
    );
  }

  getAllScheme(data: any) {
    let schemeUrl =
      '?isActive=' +
      data.isActive +
      '&page=' +
      data.currentPage +
      '&size=' +
      data.pageLimit +
      '&searchedValue=' +
      data.searchedValue +
      '&startDate=' +
      data.startDate +
      '&endDate=' +
      data.endDate;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + this.getAllSchemeURL + schemeUrl, {
      headers: headers,
      responseType: 'text',
    });
  }

  getRewardDetailsById(Id: any) {
    let URL = this.getSpecificRewardDetailsURL + Id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  
  getFinanceReviewById(id: any) {
    let URL = this.getFinanceReviewByIdURL + id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllFinanceReview(data: any) {
    let URL =
      this.getAllFinanceReviewURL +
      '?page=' +
      data.currentPage +
      '&searchValue=' +
      data.searchedValue +
      '&approvalStatus=' +
      data.approvalStatus +
      '&size=' +
      data.pageLimit +
      '&sortBy=' +
      'createdAt' +
      '&sortDirection=' + 'asc';
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllFinanceReviewProgress(data: any) {
    let URL =
      this.getAllFinanceReviewProgressURL +
      '?searchedValue='+ data.searchedValue+
      '&regionId=' + data.regionId +
      '&zoneId=' + data.zoneId +
      '&unPaged='+ data.unPaged +
      '&page=' + data.currentPage +
      '&size=' + data.pageLimit +
      '&sort=';
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getHistory(data: any) {
    let URL =
      this.getHistoryURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  getFinanceConfigData(data: any) {
    let URL =
      this.getFinanceConfigURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  
  
  exportAllRedeemedAmountHistory(data: any) {
    let URL =
      this.getAllFinanceReviewURL +
      '&searchedValue=' +
      data.searchedValue +
      '&page=' +
      data.currentPage +
      '&size=' +
      data.pageLimit + 
      '&sortDirection=' +
      'asc';

    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllScannedCategoryData(data: any) {
    let URL = this.getAllScannedCategoryURL + data.scanCategoryId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllRedeemedMethodData(data: any) {
    let URL = this.getAllRedeemedMethodURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllFinanceReviewStatusMethodData(data: any) {
    let URL = this.getAllFinanceReviewStatusMethodURL;
    return this.http.get(this.baseUrl + URL + 
      '?searchedValue=' + data.searchedValue +
      '&unPaged=' + data.unPaged +
      '&regionId=' + data.regionId +
      '&zoneId=' + data.zoneId +
      '&page=' + data.currentPage +
      '&size=' + data.pageLimit,
       {
        headers: this.authorizationKey(),
        responseType: 'text',
      });
  }

  applyConfigurations(requestPayload: any) {
    const payload = this.utility.encryptString(requestPayload)
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + this.addConfiguration, payload, {
      headers: headers,
    });
  }

  updateFinanceReviewConfigData(requestPayload: any) {
    const payload = this.utility.encryptString(requestPayload);
    const headers = this.authorizationKey();
    return this.http.post(this.baseUrl + this.updateFinanceReviewConfigURL, payload, {
      headers: headers,
    });
  }


  getAllScannedSchemes(data: any) {
    let URL =
      this.getAllScannedSchemesURL +
      '?customerTypeId=' +
      data.id +
      '&page=' +
      data.currentPage +
      '&size=' +
      data.pageLimit +
      '&searchedValue=' +
      data.searchedValue +
      '&startDate=' +
      data.startDate +
      '&endDate=' +
      data.endDate +
      '&scanCategory=' +
      data.scanCategory;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllExportRedeemedSchemes(data: any) {
    let URL =
      this.getAllExportRedeemedSchemesURL +
      '?customerTypeId=' +
      data.id +
      '&startDate=' +
      data.startDate +
      '&endDate=' +
      data.endDate +
      '&scanCategory=' +
      data.scanCategory +
      '&size=' +
      data.pageSize;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllExportScannedSchemes(data: any) {
    let URL =
      this.getAllScannedSchemesURL +
      '?customerTypeId=' +
      data.id +
      '&size=' +
      data.pageSize +
      '&searchedValue=' +
      data.searchedValue +
      '&startDate=' +
      data.startDate +
      '&endDate=' +
      data.endDate +
      '&scanCategory=' +
      data.scanCategory;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllExportSchemes(data: any) {
    let URL =
      this.getAllExportSchemesURL +
      '?startDate=' +
      data.startDate +
      '&endDate=' +
      data.endDate +
      '&profitCenterCode=' +
      data.profitCenter +
      '&isActive=' +
      data.isActive;
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
    });
  }

  getAllLeader(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseUrl +
      'customer/get-All-leaders'+
      '?searchedValue=';
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  
  getProductByID(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseUrl +
      'products/get-product-list';
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  previewInvoiceDetails(body: FormData, data: any) {
    const headers = this.authorizationKey(); 
    return this.http.post(this.baseUrl + "get/invoice/details?leaderId="+ data.leader.id, body, {
      headers: headers
    });
  }

  submitInvoiceDetails(payLoad:any, data:any){
    const headers = this.authorizationKey(); 
    return this.http.post(this.baseUrl + "upload/admin/file?"+'leaderId='+data.leaderId+'&adminId='+data.adminId , payLoad, {
      headers: headers
    });
  }
  
  submitInvoiceWithProducts(formData: FormData, leaderId: number) {
    const headers = this.authorizationKey();
    headers.delete('Content-Type'); 
    
    return this.http.post(
      `${this.baseUrl}upload-by-admin?leaderId=${leaderId}`,
      formData,
      {
        headers: headers,
        responseType: 'text' as 'json',
      }
    );
  }
  
 getAllInvoice(data:any){ 
  const headers = this.authorizationKey();
  const URL =
    this.baseUrl +
    'get/all/invoices'+
    '?searchedValue=' + data.searchedValue +
    '&unpaged=' + data.unPaged +
    '&regionId=' + data.regionId +
    '&zoneId=' + data.zoneId +
    '&status=' + data.status +
    '&page=' + data.currentPage +
    '&size=' + data.pageLimit +
    '&sortBy='+
    '&sortDir=';
  return this.http.get(URL, {
    headers: headers,
    responseType: 'text',
  });
}

  emitInvoiceEdit(value: boolean) {
    this.invoiceEditSubject.next(value);
  }

  rejectFinanceReview(financeReviewId: number, remark: string) {
    const headers = this.authorizationKey();
    
    return this.http.post(
      `${this.baseUrl}reject-finance-review-request?financeReviewId=${financeReviewId}&remark=${encodeURIComponent(remark)}`,
      '',
      { headers: headers, responseType: 'text' }
    );
  }
  
  getAllProductById(id: any) {
    const headers = this.authorizationKey();
    const URL = this.baseUrl + 'invoice-products?invoiceId=' + id;
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  
  rejectInvoice(invoiceId: number, remark: string) {
    const headers = this.authorizationKey();
    
    return this.http.post(
      `${this.baseUrl}approve-reject-invoice?invoiceId=${invoiceId}&remark=${encodeURIComponent(remark)}&approve=false`,
      '',
      { headers: headers, responseType: 'text' }
    );
  }
  
  exportAllProgressFinanceReview(data: any) {
    let URL =
      this.getAllFinanceReviewProgressURL +
      '?searchedValue='+ data.searchedValue+
      '&regionId=' +
      '&zoneId=' +
      '&unPaged='+ data.unPaged +
      '&page=' + data.currentPage +
      '&size=' + data.pageLimit +
      '&sort=';
    const headers = this.authorizationKey();
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  exportAllActiveFinanceReview(data: any) {
    const headers = this.authorizationKey();
    let URL =
      this.getAllFinanceReviewURL +
      '?searchedValue=' +
      (data.searchedValue || '') +
      '&sortBy=' +
      'createdAt' +
      '&sortDirection=' + 'asc' +
      '&unPaged=true';
  
    return this.http.get(this.baseUrl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  saveInvoiceProducts(products: any[]) {
    const payload = this.utility.encryptString(products);
    const headers = this.authorizationKey();
    return this.http.post(
      `${this.baseUrl}save-invoice-products`,
      payload,
      { headers: headers, responseType: 'text' }
    );
  }
  
  exportAllInvoice(data:any){
    const headers = this.authorizationKey();
    let URL = this.baseUrl + 'invoices/flat';
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  /**
   * Notify financial review (toggle on/off)
   * @param leaderId - The leader ID to notify
   * @returns Observable
   */
  notifyFinancialReview(leaderId: number) {
    const headers = this.authorizationKey();
    return this.http.post(
      `${this.baseUrl}notify-financialReview?leaderId=${leaderId}`,
      null,
      { headers: headers, responseType: 'text' }
    );
  }

  /**
   * Send financial review for approval (Exception tab toggle)
   * @param achievementId - The achievement ID to send for approval
   * @returns Observable
   */
  sendFinancialReviewForApproval(achievementId: number) {
    const headers = this.authorizationKey();
    return this.http.post(
      `${this.baseUrl}financial-review/send-for-approval?achievementId=${achievementId}`,
      null,
      { headers: headers, responseType: 'text' }
    );
  }

  /**
   * Approve financial review exception
   * @param achievementId - The achievement ID to approve
   * @returns Observable
   */
  approveFinancialReviewException(achievementId: number) {
    const headers = this.authorizationKey();
    return this.http.post(
      `${this.baseUrl}approve?achievementId=${achievementId}`,
      null,
      { headers: headers, responseType: 'text' }
    );
  }

  /**
   * Reject financial review exception
   * @param achievementId - The achievement ID to reject
   * @param remark - The rejection remark
   * @returns Observable
   */
  rejectFinancialReviewException(achievementId: number, remark: string) {
    const headers = this.authorizationKey();
    return this.http.post(
      `${this.baseUrl}reject?achievementId=${achievementId}&remark=${encodeURIComponent(remark)}`,
      null,
      { headers: headers, responseType: 'text' }
    );
  }
}