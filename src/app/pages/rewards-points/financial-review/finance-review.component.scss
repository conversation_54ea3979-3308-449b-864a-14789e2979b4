:host {
  // Dev server specific toast fixes
  ::ng-deep {
    .toast-container {
      position: fixed !important;
      z-index: 999999 !important; 
      pointer-events: auto !important;
      top: 12px !important;
      right: 12px !important;
      width: auto !important;
      max-width: 350px !important;
      display: block !important;
      visibility: visible !important;
      
      .ngx-toastr {
        opacity: 1 !important;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.25) !important;
        display: block !important;
        visibility: visible !important;
        pointer-events: auto !important;
      }
      
      .toast-success {
        background-color: #51A351 !important;
      }
      
      .toast-error {
        background-color: #BD362F !important;
      }
      
      .toast-info {
        background-color: #2F96B4 !important;
      }
      
      .toast-warning {
        background-color: #F89406 !important;
      }
    }
  }
}

@import "../../../theme/sass/_auth";
@import "../../../../styles";
@import "../../../theme/sass/mixins";

agm-map {
  height: 100%;
}

.app-container {
  width: 100%;
  padding-left: 14px;
  overflow-y: hidden;
  .app-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 45px;
    position: relative;
    .app-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;
      float: left;
      margin: 10px 0;
      .view-switch {
        width: 12%;
        float: right;
        @media screen and (max-width: 768px) {
          width: 100%;
        }
        .map-view {
          width: 30%;
          float: right;
          background: #a4a4a4;
          font-size: 22px;
          text-align: center;
          border-radius: 3px;
          margin: 0 0 0 11px;
          color: #ffffff;
          cursor: pointer;
        }
        .active {
          background: #102d69;
        }
        .disable {
          opacity: 0.9;
          cursor: not-allowed;
        }
      }

      .app-table {
        font-size: 15px;
        width: 100%;
        overflow-y: hidden;
        float: left;
        @media screen and (max-width: 900px) {
          width: 100%;
        }
      }

      .rewards-map {
        width: 50%;
        height: 120vh;
        float: left;
        padding: 15px 0 15px 10px;
        .name {
          line-height: 17px;
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
        @media screen and (max-width: 900px) {
          width: 100%;
        }
      }
      .no-result {
        width: 100%;
        float: left;
        text-align: center;
        line-height: 43px;
        background-color: rgba(0, 0, 0, 0.1);
        margin-top: 10px;
        font-size: 15px;
        color: #666679;
      }

      .full-width {
        width: 100%;
      }
      .hide {
        display: none;
      }

      .history-filter-container {
        display: flex;
        // margin-bottom: 5px;
        .left-column {
          width: 30%;
          .main-campaign {
            .panel {
              .panel-body {
                .wizard {
                  min-width: 80%;
                  width: 60%;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-bottom: 12px;
                  // margin-top: -22px;
                  font-size: 14px;
                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }
                  .profile-tab {
                    font-weight: 500;
                    float: left;
                    text-decoration: none;
                    text-align: center;
                    border-bottom: 3px solid #c6c6c6;
                    height: 50px;
                    line-height: 50px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 33%;
                    &:last-child {
                      border-right: none;
                    }

                    i {
                      margin-left: 5px;
                    }
                  }
                  .active {
                    border-bottom: 3px solid $button-color;
                  }
                }
                .wizard .active {
                  border-bottom: 3px solid #FF8033;
                  color: #FF8033;
                }
                .current-tab {
                  font-size: 14px;
                  border-bottom: 3px solid $button-color;
                  color: #195c94;
                  @media screen and (max-width: 500px) {
                    font-size: 8px !important;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px !important;
                  }
                }

                .search-input {
                  display: flex;
                  align-items: center;
                  width: 50%;
                }

                .history-filter {
                  .radio-container {
                    height: 40px;
                    padding-left: 30px;
                    display: flex;
                    gap: 10px;
                    vertical-align: middle;
                    align-items: flex-start;
                    position: relative;
                    bottom: 6px;
                    .radio-margin {
                      label {
                        position: relative;
                        top: 2px;
                        left: 3px;
                        font-size: 16px;
                      }
                    }
                  }
                }
                .tab-button {
                  min-width: 60%;
                  height: 40px;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-top: -15px;
                  font-size: 14px;
                  border-radius: 6px;
                  border: 1px solid rgba(0, 0, 0, 0.15);
                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }
                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }
                  .profile-tab {
                    float: left;
                    text-align: center;
                    height: 38px;
                    line-height: 50px;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    i {
                      margin-left: 5px;
                    }
                  }
                  .active {
                    border-bottom: 3px solid $button-color;
                  }
                }
                .tab-button .active {
                  border: 1px solid #FF8033;
                  color: #FF8033;
                  background-color: #e5efff;
                  font-weight: 500;
                  border-radius: 5px 0px 0px 5px;
                }
                .tab-button .activeCrop {
                  border: 1px solid #FF8033;
                  color: #FF8033;
                  width: 100% !important;
                  background-color: #e5efff;
                  font-weight: 500;
                  border-radius: 5px 5px 5px 5px !important;
                }
              }
            }

            .reward-points-filter {
              min-width: 42%;
              height: 40px;
            }
          }
        }
        .second-left-column {
          width: 50%;
        }
        .right-column {
          display: flex;
          width: 50%;
          flex-direction: column;
          justify-content: flex-end;
        }
        
        .input-group {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-wrap: nowrap;
          justify-content: flex-end;
        }
        
        .search-input {
          display: flex;
          align-items: center;
          width: 300px;
        }
        
        .input-group-add {
          display: flex;
          align-items: center;
          width: 100%;
          padding: 8px 12px;
          background-color: #fff;
          border: 1px solid #FF8033;
          border-radius: 5px;
        }
        
        .input-group-add i {
          color: #FF8033;
        }
        
        .input-group-add input {
          border: none;
          width: 100%;
          outline: none;
          font-size: 14px;
          margin-left: 5px;
        }
        
        .input-group-add input:focus {
          border: none;
        }
        
        .input-group-add img {
          height: 18px;
          cursor: pointer;
        }
        
        .export-button,
        .filter-button {
          margin-left: 5px;
        }
        
        .export-button button,
        .filter-button button {
          width: 40px;
          height: 40px;
          background-color: #FF8033;
          color: white;
          border: none;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .export-button button:hover,
        .filter-button button:hover {
          background-color: #e06c2b;
        }
        
        .export-button i,
        .filter-button i {
          color: white;
          font-size: 16px;
        }

        .addSchemeButton {
          height: 37px;
          background-color: #FF8033;
          border-radius: 0.25rem;
          border: 1px solid #FF8033;
          color: #fff;
          padding: 0px 14px 0 14px;
          width: 160px;
        }

        // Actions Dropdown Styles
        .actions-dropdown {
          position: relative;
          display: inline-block;

          .actions-dropdown-button {
            height: 37px;
            background-color: #FF8033;
            border-radius: 0.25rem;
            border: 1px solid #FF8033;
            color: #fff;
            padding: 0px 14px;
            width: 160px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;

            &:hover {
              background-color: #e06c2b;
              border-color: #e06c2b;
            }

            .dropdown-arrow {
              font-size: 12px;
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }

          .actions-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            width: 220px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            overflow: hidden;
            animation: slideDown 0.2s ease-out;

            .actions-dropdown-item {
              display: flex;
              align-items: center;
              padding: 12px 16px;
              cursor: pointer;
              transition: background-color 0.2s ease;
              border-bottom: 1px solid #f0f0f0;

              &:last-child {
                border-bottom: none;
              }

              &:hover {
                background-color: #fff5f0;
                color: #FF8033;
              }

              .action-icon {
                margin-right: 12px;
                font-size: 14px;
                width: 16px;
                text-align: center;
                color: #FF8033;
              }

              span {
                font-size: 14px;
                font-weight: 500;
                color: #333;
              }

              &:hover span {
                color: #FF8033;
              }
            }
          }

          &.open .actions-dropdown-button {
            border-radius: 0.25rem 0.25rem 0 0;
            border-bottom-color: #ddd;
          }
        }

        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
      }
    }
  }
}

.filter-main-menu-container {
  padding: 20px 20px 0px 20px !important;
  position: relative;
  top: -6px;
  .filter-menu-heading {
    .filter-title-size {
      font-size: 20px;
      font-family: sans-serif;
      color: #222222;
      font-weight: bold;
    }
  }
  .history-filter-menu-container {
    .filter-menu-input {
      margin-right: 8px;
      height: 70px;
      ::ng-deep.mat-form-field-appearance-outline .mat-form-field-outline {
        height: 36px !important;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
        padding: 1em 0 1em 0;
        height: 36px !important;
      }
      ::ng-deep .cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
        animation: cdk-text-field-autofill-end 0s 1ms;
        width: 70%;
        position: relative;
        top: -36px;
      }
      ::ng-deep .mat-icon-button {
        display: inline !important;
        bottom: 29px !important;
        left: 122px !important;
      }

      ::ng-deep
        .mat-form-field-appearance-outline
        .mat-form-field-outline-start {
        background-color: #fff;
        border-left: 1px solid #FF8033;
        border-top: 1px solid #FF8033;
        border-bottom: 1px solid #FF8033;
      }

      ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline-end {
        background-color: #fff;
        border-top: 1px solid #FF8033;
        border-right: 1px solid #FF8033;
        border-bottom: 1px solid #FF8033;
      }

      ::ng-deep .mat-form-field-flex {
        display: inline-flex;
        align-items: baseline;
        box-sizing: border-box;
        width: 103% !important;
      }

      ::ng-deep
        .mat-form-field-type-mat-date-range-input
        .mat-form-field-infix {
        width: 55% !important;
      }

      .mat-date-range-input {
        display: block;
        width: 100%;
        margin-top: -10px;
        font-size: 13px;
      }

      label {
        color: #222222;
        font-weight: 500;
      }
    }

    .area-filter-container {
      ::ng-deep .dropdown .angular2-multiselect {
        border: 1px solid #667080 !important;
      }
      label {
        color: #222222;
        font-weight: 500;
      }
      .zone-label{
        margin-top: 8px;
      }
    }
    .role-filter-container {
      margin-top: 10px;
      label {
        color: #222222;
        font-weight: 500;
      }
    }
  }

  .button-container {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 10%;
    margin-bottom: 1%;
    button {
      height: 35px;
      width: 100%;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    .btn-cancel {
      background-color: #fff;
      color: #FF8033;
      font-family: sans-serif;
      font-weight: 600;
      border: 1px solid #FF8033;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .btn-submit {
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #FF8033;
      color: #fff;
      font-family: $sans-font-family;
      font-weight: 600;
    }
    .btn-submit:disabled {
      cursor: not-allowed;
    }
  }
}

@media screen and (max-width: 1060px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
        }
      }
    }
  }
}

@media screen and (max-width: 900px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          .date-filter {
            width: 100%;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 800px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
          .date-filter {
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 80%;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 400px) {
  .app-container {
    .app-grid-container {
      .user-grid-action {
        .user-grid-action-container {
          width: 100%;
          .date-filter {
            .date-filter-title {
              width: 100% !important;
            }
            .date-filter-content {
              width: 100%;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 400px) {
  .app-container {
    .app-grid-container {
      .date-picker-container {
        width: 28%;
        height: 52px;
        mat-form-field {
          width: 92%;
          margin: 3px 0px 0px 12px;
        }
        .cancel-button {
          border: 2px solid #102d69;
        }
        .submit-button {
          color: #fff;
          background-color: #102d69;
        }
      }
    }
  }
}

:host::ng-deep .mat-form-field-flex > .mat-form-field-infix {
  padding: 0.3em 0px !important;
}
:host::ng-deep .mat-form-field-label-wrapper {
  top: -1.5em;
}

:host::ng-deep
  .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float
  .mat-form-field-label {
  transform: translateY(-1.1em) scale(0.75);
  width: 60%;
}

// .input-group {
//   flex-wrap: unset !important;
// }
:host::ng-deep.cdk-text-field-autofill-monitored:not(:-webkit-autofill) {
  margin-bottom: 2px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  display: flex;
  position: absolute;
  top: 3px;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  height: 38px;
}

:host ::ng-deep button.mat-focus-indicator.mat-icon-button.mat-button-base {
  justify-content: end !important;
}

:host ::ng-deep .mat-date-range-input-container {
  display: flex;
  align-items: center;
  width: 115%;
}

:host ::ng-deep .mat-date-range-input {
  display: block;
  width: 80%;
  font-size: 15px;
  margin-left: 6px;
}

:host ::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 0 0.15em 0 0.15em;
  margin-top: -0.25em;
  position: relative;
}

// .confirm-dialog-container {
//   position: fixed !important;
//   top: 17% !important;
//   right: 2% !important;
// }

/* Background Blur */
/* Background Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3); /* No blur, just a dark overlay */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Popup Container */
.popup-container {
  background: #fff;
  width: 50%;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  max-width: 600px;
}


/* Header */
.popup-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: bold;
}

/* Form Styling */
.popup-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
}

.form-group {
  width: 48%;
}

.form-group.full-width {
  width: 100%;
}

.form-group label {
  display: block;
  font-weight: 500;
  margin-bottom: 5px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

/* Footer Buttons */
.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.btn-cancel {
  background: #ccc;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-submit {
  background: #ff6600;
  color: #fff;
  padding: 10px 15px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.btn-submit:hover {
  background: #e65c00;
}

.reject-dialog {
  padding: 15px;
  
  .reject-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
  }
  
  .reject-content {
    margin-bottom: 20px;
    
    .reject-textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      resize: none;
      font-family: inherit;
      
      &:focus {
        outline: none;
        border-color: #ff6b35;
      }
    }
  }
  
  .reject-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    
    button {
      padding: 8px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      
      &.btn-cancel {
        background-color: #fff;
        color: #FF8033;
        font-family: sans-serif;
        font-weight: 600;
        border: 1px solid #FF8033;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      &.btn-submit {
        background-color: #FF8033;
        border: 1px solid #FF8033;
        color: white;
      }
    }
  }
}

.confirm-dialog {
  padding: 20px;
  text-align: center;
  
  .image-container {
    margin-bottom: 20px;
    
    img {
      width: 80px;
      height: auto;
    }
  }
  
  .confirm-content {
    margin-bottom: 25px;
    
    .text-fields {
      font-size: 18px;
      color: #333;
      font-weight: 500;
    }
  }
  
  .action-button-section {
    display: flex;
    justify-content: center;
    gap: 15px;
    
    .action-btn {
      padding: 8px 30px;
      border-radius: 5px;
      font-weight: 500;
      cursor: pointer;
      border: none;
      font-size: 16px;
      
      &.submit-btn {
        background-color: #FF8033;
        color: white;
        
        &:hover {
          background-color: #e06c2b;
        }
      }
      
      &.cancel-btn {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        color: #333;
        
        &:hover {
          background-color: #e5e5e5;
        }
      }
    }
  }
}

/* Make sure the dialog appears with proper styling */
::ng-deep .confirm-dialog-container {
  .mat-dialog-container {
    padding: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
}

.cdk-overlay-pane.filter-dialog-container {
  margin-top: 12.2% !important;
  margin-right: 8% !important;
}

.cdk-overlay-pane.confirm-dialog-container{
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.btn-submit:disabled {
  background-color: #ccc; /* Light gray background */
  color: #666; /* Gray text */
  cursor: not-allowed; /* Show "not-allowed" cursor */
  opacity: 0.6; /* Reduce opacity */
  cursor: default !important;
}

// Apply Configurations Dialog Styles
.apply-configurations-dialog {
  padding: 30px;
  max-width: 800px;
  width: 100%;
  background-color: #ffffff;

  .dialog-header {
    margin-bottom: 30px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin: 0;

      .required {
        color: #FF8033;
        margin-left: 2px;
      }
    }
  }

  .dialog-content {
    margin-bottom: 40px;

    .configuration-table {
      background-color: #FFF1E9;
      // border-radius: 12px;
      overflow: hidden;
      padding: 30px;

      .table-header {
        display: grid;
        grid-template-columns: 200px 1fr 1fr 1fr;
        margin-bottom: 20px;

        .header-cell {
          padding: 0 15px 15px 15px;
          font-weight: 600;
          color: #333;
          text-align: center;
          font-size: 16px;

          &:first-child {
            text-align: left;
          }
        }
      }

      .table-row {
        display: grid;
        grid-template-columns: 200px 1fr 1fr 1fr;
        margin-bottom: 20px;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .row-label {
          padding: 0 15px;
          font-weight: 600;
          color: #333;
          font-size: 16px;
          display: flex;
          align-items: center;
        }

        .input-cell {
          padding: 0 15px;
          display: flex;
          align-items: center;
          justify-content: center;

          .config-input {
            width: 120px;
            height: 45px;
            padding: 0 15px;
            border: 2px solid #333;
            border-radius: 6px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            background-color: #ffffff;
            color: #333;

            &:focus {
              outline: none;
              border-color: #FF8033;
              box-shadow: 0 0 0 2px rgba(255, 128, 51, 0.2);
            }

            &::placeholder {
              color: #666;
              font-weight: 400;
            }
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: center;
    gap: 20px;

    .btn-back {
      padding: 12px 40px;
      background-color: #ffffff;
      color: #FF8033;
      border: 2px solid #FF8033;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;

      &:hover {
        background-color: #fff5f0;
      }
    }

    .btn-submit {
      padding: 12px 40px;
      background-color: #FF8033;
      color: white;
      border: 2px solid #FF8033;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 16px;

      &:hover {
        background-color: #e06c2b;
        border-color: #e06c2b;
      }
    }
  }
}

// History Dialog Styles
.history-dialog {
  padding: 20px;
  max-width: 900px;
  width: 100%;

  .dialog-header {
    margin-bottom: 20px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0;
      text-align: center;
    }
  }

  .dialog-content {
    margin-bottom: 30px;

    .history-table-container {
      max-height: 400px;
      overflow-y: auto;
      border: 1px solid #e0e0e0;
      border-radius: 8px;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: #FF8033;
        border-radius: 4px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #e06c2b;
      }

      .history-table {
        width: 100%;

        .table-header {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr 1fr 1.2fr 1.2fr;
          background-color: #f8f9fa;
          border-bottom: 2px solid #e0e0e0;
          position: sticky;
          top: 0;
          z-index: 1;

          .header-cell {
            padding: 15px 10px;
            font-weight: 600;
            color: #333;
            text-align: center;
            border-right: 1px solid #e0e0e0;

            &:last-child {
              border-right: none;
            }

            &:first-child {
              text-align: left;
              padding-left: 15px;
            }
          }
        }

        .table-body {
          .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1.2fr 1.2fr;
            border-bottom: 1px solid #e0e0e0;

            &:last-child {
              border-bottom: none;
            }

            &:nth-child(even) {
              background-color: #f9f9f9;
            }

            .row-label {
              padding: 15px;
              font-weight: 500;
              color: #333;
              border-right: 1px solid #e0e0e0;
              display: flex;
              align-items: center;
            }

            .data-cell {
              padding: 15px 10px;
              border-right: 1px solid #e0e0e0;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #555;

              &:last-child {
                border-right: none;
              }
            }
          }
        }
      }
    }
  }

.dialog-actions {
  display: flex;
  justify-content: center;

  .dialog-close-btn {
    padding: 10px 40px;
    background-color: #FF8033;
    color: white;
    border: 2px solid #FF8033;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #e06c2b;
      border-color: #e06c2b;
    }
  }
}

}

// Dialog container styles
::ng-deep .apply-configurations-dialog-container {
  .mat-dialog-container {
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-height: 90vh;
    overflow-y: auto;
  }
}

::ng-deep .history-dialog-container {
  .mat-dialog-container {
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    max-height: 90vh;
    overflow-y: auto;
  }
}
