<div class="app-container">
  <div class="app-grid-container">
    <div class="app-grid-data-parent-container">
      <div id="foo" class="app-grid-data-container">
        <div class="history-filter-container">
          <div class="left-column second-left-column">
            <div class="main-campaign">
              <div class="panel">
                <div class="panel-body">
                  <div class="wizard">
                    <a
                      class="profile-tab history-tab-width"
                      [ngClass]="{ active: isActiveFinanceReview }"
                      (click)="financeReviewHistoryTab('isActiveFinanceReview')"
                    >
                      <span> Finance Review<span *ngIf="isActiveFinanceReview"> ({{ totalCount }})</span>
                      </span>
                    </a>
                    <a
                      class="profile-tab history-tab-width"
                      [ngClass]="{ active: isFinanceReviewProgress }"
                      (click)="financeReviewHistoryTab('isFinanceReviewProgress')"
                    >
                     <span>Exception<span *ngIf="isFinanceReviewProgress"> ({{ totalCount }})</span>
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="right-column">
            <div class="input-group">
              <div class="search-input">
                <span class="input-group-add">
                  <i class="fa fa-search" aria-hidden="true"></i>
                  <input
                    #searchBox
                    class="input-fields"
                    placeholder="Type to search"
                    [(ngModel)]="model"
                    (ngModelChange)="onSearch($event)"
                    (keypress)="funRestSearchPrevent($event)"
                  />
                  <span (click)="clearSearch()" *ngIf="model?.length">
                    <img title="Clear" src="../../../../assets/img/icons8-cancel-50.png" alt="Clear" />
                  </span>
                </span>
              </div>
              <div class="filter-button">
                <button class="add custom-button" title="Filter" (click)="isActiveFinanceReview ? filterDropdown($event) : filterDropdownProgress($event)">
                  <i class="fa fa-filter export-icon"></i>
                </button>
              </div>
              <ng-template #filterMenuDailogProgress>
                <div class="filter-main-menu-container">
                  <div class="filter-menu-heading">
                    <span class="filter-title-size">Filter By</span>
                  </div>
                  <div class="history-filter-menu-container">
                     <div class="area-filter-container">
                      <label> Region </label>
                       <angular2-multiselect [data]="regionDataList" [(ngModel)]="regionValue"
                                [settings]="regionDropdownSettings" (onSelect)="selectRegion($event)"
                                (onDeSelect)="deselectionRegion($event)" (onDeSelectAll)="deselectionAllRegion($event)"
                                [ngModelOptions]="{ standalone: true }">
                              </angular2-multiselect>
                    </div>
                     <div class="area-filter-container">
                      <label class="zone-label"> Zone </label>
                       <angular2-multiselect [data]="zoneDataList" [(ngModel)]="zoneValue" [settings]="zoneDropdownSettings"
                                (onSelect)="selectZone($event)" (onDeSelect)="deselectionZone($event)"
                                (onDeSelectAll)="deselectionAllZone($event)" [ngModelOptions]="{ standalone: true }">
                        </angular2-multiselect>
                    </div>
                  </div>
                  <div class="button-container">
                    <button type="button" class="btn-cancel" (click)="clearFilterProgress()">
                      {{ "Clear" }}
                    </button>
                    <button type="button" class="btn-submit" (click)="filterApplyProgress()">
                      {{ "Apply" }}
                    </button>
                  </div>
                </div>
              </ng-template>
              <div class="export-button">
                <button class="add" (click)="exportFinanceReviewData()" title="Export">
                  <i class="fa fa-share-square-o export-icon"></i>
                </button>
              </div>
              <div
                class="add-btn-container"
                *ngIf="isAdmin && (isActiveFinanceReview || isFinanceReviewProgress)"
              >
                <div class="actions-dropdown" [class.open]="isActionsDropdownOpen">
                  <button
                    class="actions-dropdown-button"
                    (click)="toggleActionsDropdown()"
                    title="Actions"
                  >
                    Actions
                    <i class="fa fa-chevron-down dropdown-arrow" [class.rotated]="isActionsDropdownOpen"></i>
                  </button>
                  <div class="actions-dropdown-menu" *ngIf="isActionsDropdownOpen">
                    <div class="actions-dropdown-item" *ngIf="!hasExistingConfiguration" (click)="handleApplyConfigurations()">
                      <i class="fa fa-plus action-icon"></i>
                      <span>Apply Configurations</span>
                    </div>
                    <div class="actions-dropdown-item" *ngIf="hasExistingConfiguration" (click)="handleEditConfigurations()">
                      <i class="fa fa-edit action-icon"></i>
                      <span>Edit Configurations</span>
                    </div>
                    <div class="actions-dropdown-item" (click)="handleHistory()">
                      <i class="fa fa-history action-icon"></i>
                      <span>History</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
                  
        </div>
        <div class="app-table">
          <dynamic-table
            [tableHeads]="tableHead"
            [tableData]="financeReviewHistoryData"
            [tableConfiguration]="configurationSettings"
            [tableColName]="tableColName"
            (pageChange)="getPageData($event)"
            (editFormClose)="editFinanceReview($event)"
            (onApproveFinanceReview)="approveFinanceReview($event)"
            (onRejectFinanceReview)="openRejectDialog($event)"
            [showIndex]="showIndex"
          >
          </dynamic-table>
        </div>
        <div class="no-result" *ngIf="isMap && !financeReviewHistoryData.length">
          No data found
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #filterMenuDailog>
  <div class="filter-main-menu-container">
    <div class="filter-menu-heading">
      <span class="filter-title-size">Filter By</span>
    </div>
    <div class="history-filter-menu-container">
      <div class="area-filter-container">
        <label> Finance Review Status</label>
        <angular2-multiselect
          class="dropdown"
          [data]="redeemedMethodDetails"
          [(ngModel)]="redeemedMethod"
          [settings]="redeemedMethodSeasonDropdownSettings"
          (onSelect)="onRedeemedMethodHistorySelected($event)"
          (onDeSelect)="onRedeemedMethodHistoryDeSelected($event)"
          (onDeSelectAll)="onRedeemedMethodHistoryDeSelectedAll($event)"
          [ngModelOptions]="{ standalone: true }">
        </angular2-multiselect>
      </div>
    </div>
    <div class="button-container">
      <button type="button" class="btn-cancel" (click)="clearFilter()">
        {{ "Clear" }}
      </button>
      <button type="button" class="btn-submit" (click)="filterApply()">
        {{ "Apply" }}
      </button>
    </div>
  </div>
</ng-template>

<app-target-modal
  [style.display]="modalOpen ? 'block' : 'none'" 
  [isOpen]="modalOpen" 
  [modalType]="modalMode" 
  [addTargetType]="true"
  (close)="modalOpen = false" 
  >
  
</app-target-modal>

<ng-template #rejectDialog>
  <div class="reject-dialog">
    <h2 class="reject-title">Add Comment</h2>
    <div class="reject-content">
      <textarea 
        [(ngModel)]="rejectRemark" 
        placeholder="Enter your comment here..." 
        class="reject-textarea"
        rows="5">
      </textarea>
    </div>
    <div class="reject-actions">
      <button class="btn-cancel" (click)="cancelReject()">Cancel</button>
      <button class="btn-submit" (click)="submitReject()" [disabled]="!rejectRemark || rejectRemark.length === 0">Submit</button>
    </div>
  </div>
</ng-template>

<ng-template #approveConfirmDialog>
  <div class="confirm-dialog">
    <div class="image-container">
      <img src="../../../assets/img/finance-review-dialog.svg" alt="dialog" />
    </div>
    <div class="confirm-content">
      <span class="text-fields">
        Are you sure you want to approve this finance review?
      </span>
    </div>
    <div class="action-button-section">
      <button class="action-btn submit-btn" (click)="confirmApproval()">
        Yes
      </button>
      <button class="action-btn cancel-btn" (click)="dialog.closeAll()">
        No
      </button>
    </div>
  </div>
</ng-template>

<ng-template #applyConfigurationsDialog>
  <div class="apply-configurations-dialog">
    <div class="dialog-header">
      <h2>{{configurationDialogTitle}}<span class="required">*</span></h2>
    </div>
    <div class="dialog-content">
      <div class="configuration-table">
        <div class="table-header">
          <div class="header-cell">Leader Type</div>
          <div class="header-cell">%NPP</div>
          <div class="header-cell">%CP</div>
          <div class="header-cell">%Total</div>
        </div>
        <div class="table-row">
          <div class="row-label">Leaders</div>
          <div class="input-cell">
            <input type="number" [(ngModel)]="configurationData.leaders.npp" placeholder="25%" class="config-input">
          </div>
          <div class="input-cell">
            <input type="number" [(ngModel)]="configurationData.leaders.cp" placeholder="35%" class="config-input">
          </div>
          <div class="input-cell">
            <input type="number" [(ngModel)]="configurationData.leaders.total" placeholder="95%" class="config-input">
          </div>
        </div>
        <div class="table-row">
          <div class="row-label">Grower</div>
          <div class="input-cell">
            <input type="text" [(ngModel)]="configurationData.grower.npp" placeholder="NA" class="config-input">
          </div>
          <div class="input-cell">
            <input type="text" [(ngModel)]="configurationData.grower.cp" placeholder="NA" class="config-input">
          </div>
          <div class="input-cell">
            <input type="number" [(ngModel)]="configurationData.grower.total" placeholder="100%" class="config-input">
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-actions">
      <button class="btn-back" (click)="closeApplyConfigurationsDialog()">Back</button>
      <button class="btn-submit" (click)="submitConfigurations()">Submit</button>
    </div>
  </div>
</ng-template>

<ng-template #historyDialog>
  <div class="history-dialog">
    <div class="dialog-header">
      <h2>History</h2>
    </div>
    <div class="dialog-content">
      <div class="history-table-container">
        <div class="history-table">
          <div class="table-header">
            <div class="header-cell"></div>
            <div class="header-cell">%NPP</div>
            <div class="header-cell">%CP</div>
            <div class="header-cell">%Total</div>
            <div class="header-cell">Updated By</div>
            <div class="header-cell">Updated On</div>
          </div>
          <div class="table-body">
            <ng-container *ngFor="let item of historyData">
              <div class="table-row">
                <div class="row-label">{{item.type}}</div>
                <div class="data-cell">{{item.npp}}</div>
                <div class="data-cell">{{item.cp}}</div>
                <div class="data-cell">{{item.total}}</div>
                <div class="data-cell">{{item.updatedBy}}</div>
                <div class="data-cell">{{item.updatedOn}}</div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-actions">
      <button class="dialog-close-btn" (click)="closeHistoryDialog()">Close</button>
    </div>

  </div>
</ng-template>