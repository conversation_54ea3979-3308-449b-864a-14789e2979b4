import { Component, OnChanges, OnInit, SimpleChanges, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import * as _ from 'lodash';
import moment from 'moment';
import { debounceTime, Subject, tap } from 'rxjs';
import { ngxCsv } from 'ngx-csv';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Utility } from "src/app/shared/utility/utility";
import { SidebarServiceService } from 'src/app/app-services/sidebar-service.service';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { DynamicTableComponent } from 'src/app/shared/data-table/data-table.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatButtonModule } from '@angular/material/button';
import { MatPaginatorModule } from '@angular/material/paginator';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { GlobalEvents } from 'src/app/helpers/global.events';
import { RewardPointsService } from 'src/app/app-services/reward-points-service';
import { AppConstant } from 'src/app/constants/app.constant';
import { BaThemeSpinner } from 'src/app/theme/services/baThemeSpinner/baThemeSpinner.service';
import { AuthenticationHelper } from 'src/app/helpers/authentication';
import { TargetModalComponent } from "../../../modals/target-modal/target-modal.component";
import { ToastFixService } from 'src/app/shared/services/toast-fix.service';
import { UserService } from 'src/app/app-services/user-service';
import { NumberFormatService } from 'src/app/shared/shared/number-format.service';

interface ConfigurationSettings {
  showPagination: boolean;
  perPage: number;
  totalRecordCount: number;
  currentPage: number;
  showActionsColumn: boolean;
  actionsColumnName: string;
  noDataMessage: string;
  showEdit: boolean;
  isApprove: boolean,
  isRejected: boolean
}

@Component({
  selector: 'app-target',
  templateUrl: './target.component.html',
  styleUrls: ['./target.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatInputModule,
    MatButtonToggleModule,
    NgxPaginationModule,
    MatNativeDateModule,
    DynamicTableComponent,
    MatDatepickerModule,
    MatFormFieldModule,
    MatButtonModule,
    MatPaginatorModule,
    AngularMultiSelectModule,
    TargetModalComponent,
  ],
})
export class TargetComponent implements OnInit, OnChanges {
  @ViewChild('filterMenuDailog') filterMenuDailog!: TemplateRef<any>;
  @ViewChild('addEditSlabDailog') addEditSlabDailog!: TemplateRef<any>;
  @ViewChild('filterMenuDailogProgress') filterMenuDailogProgress!: TemplateRef<any>;
  modalOpen: boolean = false;
  modalMode: 'edit' | 'add' = 'edit'; // or any modal modes you handle
  targetDefaults: any = {}; // holds the data to send to the modal

  filterMenuDailogRef!: MatDialogRef<any>;
  filterMenuDailogProgressRef!: MatDialogRef<any>;
  rewardsHistoryData: any = [];
  isActiveTarget: boolean = true;
  isTargetProgress: boolean = false;
  isForagesDistributor: boolean = true;
  isFcDistributor: boolean = false;
  isForagesRetailer: boolean = false;
  isFcRetailer: boolean = false;
  redeemedHistoryFcSeasonDropdownData: any = [];
  redeemedHistoryFCSeason: any = [];
  redeemedHistoryForagesSeasonDropdownData: any = [];
  redeemedHistoryForagesSeason: any = [];
  tableHead: any = [];
  tableColName: any = [];
  totalRecordCount = 0;
  perPage = AppConstant.PER_PAGE_ITEMS;
  showIndex: any = { index: null };
  redeemedMethod: any = [];
  redeemedMethodDetails: any = [];
  selectedSeasonDropdownData: number = 0;
  selectedRedeemedMethod: string = '';
  regionDataList: any = [];
  zoneDataList: any = [];
  selectedRegion: any = [];
  selectedZone: any = [];
  regionValue: any;
  zoneValue: any;
  redeemedHistorySeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Season-Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedHistoryForagesSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Year',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  redeemedMethodSeasonDropdownSettings = {
    singleSelection: true,
    clearOnSelection: false,
    showUncheckAll: false,
    text: 'Select Target Status',
    enableSearchFilter: false,
    classes: 'myclass customer-dd brands-multi-select',
    labelKey: 'name',
    idField: 'id',
    enableFilterSelectAll: false,
    maxHeight: 200,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  dateForm: FormGroup;
  configurationSettings: ConfigurationSettings = {
    showPagination: true,
    perPage: AppConstant.PER_PAGE_ITEMS,
    totalRecordCount: 0,
    currentPage: 0,
    showActionsColumn: true,
    actionsColumnName: 'Actions',
    noDataMessage: 'No data found',
    showEdit: true,
    isApprove: true,
    isRejected: true
  };
  model: any;
  showErrorBlockSection: boolean = false;
  showOtherFilters: any = {
    showRadioFilters: false,
    showSearch: true,
    add: false,
    showdropdown1Filters: false,
    showdropdown2Filters: false,
    showSearchiconFilters: false,
    showReport: false,
    export: true,
  };
  userButton: any = [];
  custId: any = '1';
  startDate: any;
  endDate: any;
  modelDate: any;
  endDateModel: any = '';
  startDateModel: any = '';
  isDateFilter: boolean = false;
  currentPage: number = 0;
  searchedString: string = '';
  isSearch: boolean = false;
  isAdmin: boolean = false;
  modelChanged: Subject<string> = new Subject<string>();
  exportData: any = [];
  rewardFilters: any = '';
  date: any;
  currentDate: any;
  cropTab = '';
  firstDay: any;
  formattedFirstDay: any;
  isTable: boolean = true;
  isMap: boolean = false;
  dateData: any;
  toDateAPI!: string;
  fromDateAPI!: string;
  startDateAPI!: string;
  endDateAPI!: string;
  minDate: any;
  maxDate: any;
  dateRange!: FormGroup;
  isFieldCrop: boolean = true;
  isForages: boolean = false;
  userInfo: any;
  userBusinessUnit: any;
  roleId: any;
  totalCount: number = 0;
  addEditSlabDailogRef: MatDialogRef<unknown, any> | undefined;

  containers = [
    { icon: '📄', text: 'Upload PDF here', buttonText: 'Upload PDF' },
    { icon: '📄', text: 'Upload XML here', buttonText: 'Upload XML' },
    { icon: '📄', text: 'Upload File here', buttonText: 'Upload File' }
  ];
  userRole: string = '';

  constructor(
    private routes: ActivatedRoute,
    private userService: UserService,
    private router: Router,
    private fb: FormBuilder,
    private rewardPointsService: RewardPointsService,
    public toastr: ToastrService,
    private _spinner: BaThemeSpinner,
    private events: GlobalEvents,
    private rewardService: RewardPointsService,
    private utility: Utility,
    private rewardPointService: RewardPointsService,
    private sidebarService: SidebarServiceService,
    public dialog: MatDialog,
    private toastFixService: ToastFixService,
    private numberFormatService: NumberFormatService 
  ) {
    this.roleId = localStorage.getItem('roleID');
    this.userInfo = localStorage.getItem('userInfo');
    this.userBusinessUnit = JSON.parse(this.userInfo)?.scanCategory?.id;
    this.events.setChangedContentTopText('Target Management');
    this._spinner.hide();
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
    let isValue: any = AuthenticationHelper.getRoleID();
    this.dateData = moment(new Date()).add(1, 'days');
    this.dateForm = this.fb.group({
      startDatePicker: [this.fromDateAPI],
      endDatePicker: [this.endDateAPI],
    });
    this.rewardService._tableDialogBox.emit('hide');
  }

  ngOnInit() {
    const localRole = AuthenticationHelper.getRole()?.trim().toUpperCase();
    if (localRole) {
      this.setRoleBasedConfig(localRole);
      this._spinner.hide();
    }
    this.userService.userRole$.subscribe(apiRole => {
      const apiRoleFormatted = apiRole?.trim().toUpperCase();
      if (apiRoleFormatted && apiRoleFormatted !== localRole) {
        this.setRoleBasedConfig(apiRoleFormatted);
        this._spinner.hide();
      }
    });

    this._spinner.hide();
    this.setDateRange();
    this.rewardFilters = 'redeemed';
    this.cropTab = 'TARGET';
    window.scrollTo(0, 0);
    this._spinner.hide();
    // only emit if value is different from previous value
    this.modelChanged.pipe(
      debounceTime(600),
    ).subscribe((model: any) => {
      if (model?.trim()) {
        this.isSearch = true;
        this.searchedString = model.trim();
        if (this.isTargetProgress) {
          this.getAllTargetProgressData(1);
        } else {
          this.getPageData(1);
        }
      } else {
        this.isSearch = false;
        this.searchedString = '';
        if (this.isTargetProgress) {
          this.getAllTargetProgressData(1);
        } else {
          this.getPageData(1);
        }
      }
    });
    this.activeTabRoleBased();
  }

  ngOnChanges(changes: SimpleChanges): void {
  }

  private setRoleBasedConfig(currentRole: string) {
    this.isAdmin = currentRole === 'ADMIN';
    this.userRole = currentRole;
    this.configurationSettings.showEdit = this.isAdmin;
  }

  editTarget(data: any) {
    // if (data?.leaderId) {
    //   this.rewardPointsService.editTarget(data).subscribe({
    //     next: (response: any) => {
    //       try {
    //         // Check if response needs decryption
    //         if (typeof response === 'string' && response) {
    //           try {
    //             const decrypted = this.utility.decrypt(response);
    //             response = JSON.parse(decrypted);
    //           } catch (e) {
    //             // If decryption fails, use original response
    //           }
    //         }
            
    //         this.toastr.success("Target edited successfully");
    //         this.getAllTargetData();
    //       } catch (error) {
    //         console.error('Error processing response:', error);
    //         this.toastr.error("Failed to edit target");
    //       }
    //     },
    //     error: (error: any) => {
    //       try {
    //         if (typeof error.error === 'string') {
    //           const decrypted = this.utility.decrypt(error.error);
    //           error = JSON.parse(decrypted);
    //         }
    //         this.toastr.error(error.message || "Failed to edit target");
    //       } catch (e) {
    //         this.toastr.error("Failed to edit target");
    //       }
    //     }
    //   });
    // }
  }

  openAddSlabDialog(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);

    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      data: data,
      hasBackdrop: true,
    });

    this.addEditSlabDailogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  handleOpenTargetPopUp() {
    this.modalOpen = true;
    this.modalMode = 'add';
  }

  handleTargetSubmit(requestBody: any) {
    if (!requestBody?.leaderId) {
      return;
    }
    // this.rewardPointService.addTarget(requestBody).subscribe({
    //   next: (targetDetails: any) => {
    //     this.modalOpen = false
    //     if (targetDetails) {
    //       this.toastr.success(targetDetails);
    //       this.fixToastVisibility();
    //     } else {
    //       this.toastr.error(targetDetails.message);
    //       this.fixToastVisibility();
    //     }
    //     this.targetDefaults = {};
    //     this.getAllTargetData();
    //     this._spinner.hide();
    //     this.modalOpen = false
    //   },
    //   error: (errorResponse: any) => {
    //     errorResponse = (errorResponse.error);
    //     errorResponse = JSON.parse(errorResponse);
    //     this.toastr.error(errorResponse.message);
    //     this.fixToastVisibility();
    //   },
    // });
  }

  openEditTargetDialog(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);

    this.addEditSlabDailogRef = this.dialog.open(this.addEditSlabDailog, {
      width: '600px',
      disableClose: false,
      panelClass: 'custom-popup',
      data: data,
      hasBackdrop: true,
    });

    this.addEditSlabDailogRef.afterClosed().subscribe(() => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  activeTabRoleBased() {
    if (this.userBusinessUnit == 2) {
      this.totalCount = 0;
      this.isFieldCrop = false;
      this.isForages = true;
      this.isActiveTarget = true;
      this.isTargetProgress = false;
      // this.tabChanged('isForages');
      this.rewardHistoryTab('isTarget');
      this.getAllRedeemedMethodData();
    } else if (this.userBusinessUnit == 1) {
      this.totalCount = 0;
      this.isFieldCrop = true;
      this.isForages = false;
      this.isActiveTarget = true;
      this.isTargetProgress = false;
      // this.tabChanged('isFieldCrop');
      this.rewardHistoryTab('isTargetProgress');
      this.getAllRedeemedMethodData();
    } else if (this.roleId == 1 && this.userBusinessUnit == null) {
      this.totalCount = 0;
      this.isActiveTarget = true;
      this.isTargetProgress = false;
      this.rewardHistoryTab('isActiveTarget');
      this.getAllRedeemedMethodData();
    }
  }

  ngOnDestroy() {
    this.modelChanged.unsubscribe();
  }

  setDateRange() {
    this.dateRange = this.fb.group({
      start: [''],
      end: [''],
    });
  }

  /**
   * Called for creating a date object
   * @param date
   * @returns {{date: {year: number, month: number, day: (number|Date)}}}
   */
  setDateObject(date: any) {
    if (date) {
      const dateObject = {
        date: {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          day: date.getDate(),
        },
      };
      return dateObject;
    } else {
      return null;
    }
  }

  /**
   * Method for setting the headers of the table for different customer types
   */
  setTableHeader() {
    if (this.isActiveTarget) {
      this.tableHead = [
        'Leader Name',
        'Region',
        'Zone',
        'RFC (Tax ID)',
        'CP (Mex$)',
        'NPP (Mex$)',
        'Total (Mex$)',
        'Approved on',
        'Approved by',
        'Status',
      ];

      this.tableColName = [
        'leaderName',
        'region',
        'zone',
        'taxId',
        'cropProtectionAmount',
        'nppAmount',
        'amount',
        'approvedOn',
        'approvedBy',
        'approvalStatus',
      ];
      this.rewardsHistoryData = [];
      this.getAllTargetData();
    } else {
      this.tableHead = [
        'Leader Name',
        'Region',
        'Zone',

        'CP(Mex$)',
        'Achieved CP(Mex$)',
        'Achieved CP(%)',

        'NPP(Mex$)',
        'Achieved NPP(Mex$)',
        'Achieved NPP(%)',

        'Total Target(Mex$)',
        'Achieved Total(Mex$)',
        'Achieved Total(%)',

        'CP Bonus(Mex$)',
        'NPP Bonus(Mex$)',
        'Total Bonus(Mex$)',

        'Telephone',
        'Email',
        'Company Name',
        'Main Crop',
      ];
      this.tableColName = [
        'progressLeaderName',
        'progressRegion',
        'progressZone',

        'progressCPMex',
        'progressMainCrop',
        'progressAdvanceCrop',
        'progressNPPMex',

        'progressNPPPurchases',
        'progressNPPAdvance',
        'progressTotalMex',
        'progressTotalPurchases',
        'progressTotalAdvance',

        'progressBonusCrop',
        'progressNPPBonus',
        'progressTotalBonus',
        'progressTelephone',
        'progressEmail',
        'companyName',
        'progressCropsInTheArea',
      ];
      this.getAllTargetProgressData();
      this.getRegion();
      this.rewardsHistoryData = [];
    }
  }

  tabChanged(tabChangeEvent: any): void {
    this.cropTab = tabChangeEvent;
  }

  /**
   * Method for changing customer tabs
   * @param data
   */
  userRoleRewardPoints: string = '';
  rewardHistoryTab(data: any) {
    switch (data) {
      case 'isActiveTarget':
        this.rewardFilters = 'redeemed';
        this.userRoleRewardPoints = 'isActiveTarget';
        this.totalCount = 0;
        this.isActiveTarget = true;
        this.isTargetProgress = false;
        this.currentPage = 0;
        this.configurationSettings.currentPage = 0;
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.router.navigate(['/target-management']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.configurationSettings.showActionsColumn = true;
        this.setTableHeader();
        break;
      case 'isTargetProgress':
        this.rewardFilters = 'redeemed';
        this.userRoleRewardPoints = 'isTargetProgress';
        this.totalCount = 0;
        this.isActiveTarget = false;
        this.isTargetProgress = true;
        this.currentPage = 0;
        this.configurationSettings.currentPage = 0;
        this.redeemedMethod = [];
        this.selectedRedeemedMethod = '';
        this.selectedSeasonDropdownData = 0;
        this.redeemedHistoryForagesSeason = [];
        this.redeemedHistoryFCSeason = [];
        this.searchedString = '';
        this.model = '';
        this.router.navigate(['/target-management']);
        this.startDate = '';
        this.endDate = '';
        this.startDateModel = '';
        this.endDateModel = '';
        this.fromDateAPI = '';
        this.toDateAPI = '';
        this.startDateAPI = '';
        this.endDateAPI = '';
        this.dateForm.controls['startDatePicker'].setValue('');
        this.dateForm.controls['endDatePicker'].setValue('');
        this.configurationSettings.showActionsColumn = false;
        this.setTableHeader();
        break;
      default:
        this.totalCount = 0;
        this.rewardFilters = 'redeemed';
        this.isActiveTarget = true;
        this.isTargetProgress = false;
        this.isFieldCrop = true;
        this.isForages = false;
        this.currentPage = 0;
        this.configurationSettings.currentPage = 0;
        this.searchedString = '';
        this.model = '';
        this.selectedRedeemedMethod = '';
        this.redeemedMethod = [];
        this.router.navigate(['/target-management']);
        this.setTableHeader();
        break;
    }
  }

  /**
   * Called to get the data according to the searched query
   * @param event
   */
  onSearch(event: any) {
    this.modelChanged.next(event);
  }

  /**
   * Method for clearing search query
   */
  clearSearch() {
    this.searchedString = '';
    this.model = '';
    this.isSearch = false;
    if (this.isTargetProgress) {
      this.getAllTargetProgressData();
    } else {
      this.getAllTargetData();
    }
  }

  filterDropdown(data?: any) {
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogRef = this.dialog.open(this.filterMenuDailog, {
      width: '16%',
      height: '200px',
      position: {
        top: '17%',
        right: '2%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'filter-dialog-container',
      data: data,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  filterDropdownProgress(data?: any){
    this.rewardPointService._disabledSidebar.emit(true);
    this.rewardPointService._openedPopup.emit(true);
    this.filterMenuDailogProgressRef = this.dialog.open(this.filterMenuDailogProgress, {
      width: '18%',
      height: '260px',
      position: {
        top: '17%',
        right: '2%',
      },
      backdropClass: 'custom-backdrop',
      panelClass: 'filter-dialog-container',
      data: data,
      disableClose: false,
      hasBackdrop: true,
    });
    this.filterMenuDailogProgressRef.afterClosed().subscribe((result: any) => {
      this.rewardPointService._disabledSidebar.emit(false);
      this.rewardPointService._sidebarPin.emit(false);
    });
  }

  clearFilter() {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.selectedRedeemedMethod = '';
    this.redeemedMethod = [];
    this.selectedRedeemedMethod = '';
    this.filterMenuDailogRef.close();
    this.getAllTargetData();
  }

  filterApply() {
    // if (this.endDate) {
    //   this.getAllTargetData();
    //   this.filterMenuDailogRef.close();
    // } else {
    //   this.toastr.warning('Please select end date');
    // }

    if (this.startDate && !this.endDate) {
      this.toastr.warning('Please select end date');
    } else
      if (!this.startDate && !this.endDate) {
        this.getAllTargetData();
        this.filterMenuDailogRef.close();
      }
      else if (this.startDate && this.endDate) {
        this.getAllTargetData();
        this.filterMenuDailogRef.close();
      }
  }

  /**
   * Method for select season
   */
  onRedeemedHistorySeasonSelected(event: any) {
    this.selectedSeasonDropdownData = event.id;
    this.getAllTargetData();
  }

  /**
   * Method for deselect season
   */
  onRedeemedHistorySeasonDeSelected(event: any) {
    this.selectedSeasonDropdownData = 0;
    this.getAllTargetData();
  }

  /**
   * Method for deselect All season
   */

  onRedeemedHistorySeasonDeSelectedAll(event: any) {
    this.selectedSeasonDropdownData = 0;
    this.getAllTargetData();
  }

  /**
   * Method for select redeemed method
   */
  onRedeemedMethodHistorySelected(event: any) {
    this.selectedRedeemedMethod = event.method;
  }

  /**
   * Method for deselect redeemed method
   */
  onRedeemedMethodHistoryDeSelected(event: any) {
    this.selectedRedeemedMethod = '';
    this.getAllTargetData();
  }

  /**
   * Method for deselect season
   */
  onRedeemedMethodHistoryDeSelectedAll(event: any) {
    this.selectedRedeemedMethod = '';
    this.getAllTargetData();
  }

  /**
   * API call for getting the redeemed history data
   * @param page
   */
  getAllTargetData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      startDate: this.fromDateAPI ? this.fromDateAPI : '',
      customerTypeId: this.isActiveTarget ? 1 : 2,
      approvalStatus: this.selectedRedeemedMethod
        ? this.selectedRedeemedMethod
        : '',
      unPaged: false,
    };
    this._spinner.show();
    const rewards = this.rewardPointsService.getAllTarget(data);

    rewards.subscribe({
      next: (scheme: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          
          this.rewardsHistoryData = [];
          if (schemeData?.content?.length) {
            schemeData.content.forEach((schemeInfo: any) => {
              const is_Assigned = schemeInfo.is_Assigned;
              const isDisabled = is_Assigned === false || is_Assigned === "false" || is_Assigned === 0 || is_Assigned === "0";
              const schemeInfoObj = {
                targetId: schemeInfo.id,
                leaderId: schemeInfo.leaderId || schemeInfo.id,
                region: schemeInfo.region || 'NA',
                zone: schemeInfo.zone || 'NA',
                portfolio: schemeInfo.portfolio || 'NA',
                cropProtectionAmount: this.numberFormatService.formatNumber(schemeInfo.cropProtectionAmount),
                nppAmount: this.numberFormatService.formatNumber(schemeInfo.nppAmount),
                amount: this.numberFormatService.formatNumber(schemeInfo.totalAmount),
                startDate: schemeInfo.startDate,
                endDate: schemeInfo.endDate,
                isActive: schemeInfo.isActive,
                approvalStatus: schemeInfo.approvalStatus || 'NA',
                taxId: schemeInfo.rfc || 'NA',
                leaderName: this.utility.toUpperCaseUtil(schemeInfo.leaderName) || 'NA',
                is_Assigned: schemeInfo.is_Assigned,
                isDisabled: isDisabled,
                grower: schemeInfo.grower || false,
                approvedOn: schemeInfo.approvedOn ? moment(schemeInfo.approvedOn).format('DD-MM-YYYY') : 'NA',
                approvedBy: schemeInfo.approvedBy || 'NA'
              };
              this.rewardsHistoryData.push(schemeInfoObj);
            });
            this.configurationSettings.totalRecordCount = schemeData?.totalElements || schemeData?.numberOfElements;
            this.totalCount = schemeData?.totalElements || schemeData?.numberOfElements;
            this.events.setChangedContentTopText('Target Management');
          } else {
            this.totalRecordCount = 0;
            this.totalCount = 0;
            this.events.setChangedContentTopText(
              'Target Management'
            );
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to load target data");
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          this.toastr.error("Failed to load target data");
        }
      },
    });
  }

  getAllTargetProgressData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      unPaged: false,
      regionId: this.selectedRegion ? this.selectedRegion : '',
      zoneId: this.selectedZone ? this.selectedZone : '',
    };
    this._spinner.show();
    const rewards = this.rewardPointsService.getAllTargetProgress(data);

    rewards.subscribe({
      next: (scheme: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = scheme;
          if (typeof scheme === 'string') {
            try {
              parsedResponse = JSON.parse(scheme);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(scheme);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let schemeData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            schemeData = JSON.parse(decrypted);
          } else {
            schemeData = parsedResponse;
          }
          
          this.rewardsHistoryData = [];
          if (schemeData?.content?.length) {
            schemeData.content.forEach((schemeInfo: any) => {
              const is_Assigned = schemeInfo.is_Assigned;
              const isDisabled = is_Assigned === false || is_Assigned === "false" || is_Assigned === 0 || is_Assigned === "0";
              const schemeInfoObj = {
                progressRegion: schemeInfo.region ?? 'NA',
                progressZone: schemeInfo.zone ?? 'NA',
                progressLeaderName: this.utility.toUpperCaseUtil(schemeInfo.name) ?? 'NA',

                progressCPMex: this.numberFormatService.formatNumber(schemeInfo.totalCropProtectionAmount),
                progressMainCrop: this.numberFormatService.formatNumber(schemeInfo.achievedCropProtectionAmount) || 0,
                progressAdvanceCrop: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalCropProtectionPercentage) || 0,

                progressNPPMex: this.numberFormatService.formatNumber(schemeInfo.totalNppAmount),
                progressNPPPurchases: this.numberFormatService.formatNumber(schemeInfo.achievedNppAmount) || 0,
                progressNPPAdvance: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalNppPercentage)|| 0,

                progressTotalMex: this.numberFormatService.formatNumber(schemeInfo.totalTargetAmount),
                progressTotalPurchases: this.numberFormatService.formatNumber(schemeInfo.achievedTotalAmount) ?? 0,
                progressTotalAdvance: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalPercentage)|| 0,

                progressBonusCrop: this.numberFormatService.formatNumber(schemeInfo.cropProtectionBonus)|| 0,
                progressNPPBonus: this.numberFormatService.formatNumber(schemeInfo.nppBonus)|| 0,
                progressTotalBonus: this.numberFormatService.formatNumber(schemeInfo.totalBonus) || 0,

                progressTelephone: schemeInfo.mobileNo ?? 'NA',
                progressEmail: schemeInfo.email ?? 'NA',
                progressCropsInTheArea: this.utility.capitalizeWords(schemeInfo.crop) ?? 'NA',
                companyName: this.utility.toUpperCaseUtil(schemeInfo.company) ?? 'NA',
              };
              this.rewardsHistoryData.push(schemeInfoObj);
            });
            this.configurationSettings.totalRecordCount = schemeData?.totalElements || schemeData?.numberOfElements;
            this.totalCount = schemeData?.totalElements || schemeData?.numberOfElements;
            this.events.setChangedContentTopText('Target Management');
          } else {
            this.totalRecordCount = 0;
            this.totalCount = 0;
            this.events.setChangedContentTopText('Target Management');
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to load target progress data");
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message);
          }
        } catch (e) {
          this.toastr.error("Failed to load target data");
        }
      },
    });
  }


  endDateChanged(event: any) {
    if (!this.endDate) {
      this.toastr.warning('Please select end date');
    } else {
      this.fromDateAPI = moment(this.startDate, 'DD-MM-YYYY').format(
        'DD-MM-YYYY'
      );
      this.toDateAPI = moment(this.endDate, 'DD-MM-YYYY').format('DD-MM-YYYY');
    }
  }

  /**
   * To handle the scannig category dropdown
   */

  /**
   * To handle the redeemed method dropdown
   */
  getAllRedeemedMethodData() {
    let data = {
      scanCategoryId: this.isFieldCrop ? 1 : 2,
    };
    this.rewardPointsService.getAllTargetStatusMethodData().subscribe({
      next: (redeemedMethodDetails: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = redeemedMethodDetails;
          if (typeof redeemedMethodDetails === 'string') {
            try {
              parsedResponse = JSON.parse(redeemedMethodDetails);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(redeemedMethodDetails);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let methodData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            methodData = JSON.parse(decrypted);
          } else {
            methodData = parsedResponse;
          }
          
          this.redeemedMethodDetails = [];
          methodData.forEach(
            (redeemedMethodDetails: any, index: number) => {
              let redeemedDataObj;
              if (index < 2) {
                redeemedDataObj = {
                  name: redeemedMethodDetails,
                  id: index + 1,
                  method: redeemedMethodDetails,
                };
              } else {
                redeemedDataObj = {
                  name: redeemedMethodDetails,
                  id: index + 1,
                  method: redeemedMethodDetails,
                };
              }

              this.redeemedMethodDetails.push(redeemedDataObj);
            }
          );
        } catch (error) {
          console.error('Error processing response:', error);
        }
      },
      error: (errorResponse: any) => {
        let error: any = (errorResponse.error)
        error = JSON.parse(error);
        if ('Full authentication is required to access this resource' == error.message) {
          localStorage.clear();
          this.router.navigate(['']);
          this.toastr.success('Signed Out Successfully');
        }
        else {
          this.toastr.error(error.message);
        }
      },
    });
  }

  /**
   * API call for getting the redeemed history data
   * @param page
   */
  exportTargetData(page?: any) {
    if (this.isActiveTarget === true) {
      this.exportActiveTargetData(page);
    } else if (this.isActiveTarget === false) {
      this.exportTargetProgressData(page);
    }
  }
  exportTargetProgressData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      startDate: this.fromDateAPI ? this.fromDateAPI : '',
      customerTypeId: this.isActiveTarget ? 1 : 2,
      scanningCategoryId: this.selectedSeasonDropdownData
        ? this.selectedSeasonDropdownData
        : 0,
      approvalStatus: this.selectedRedeemedMethod
        ? this.selectedRedeemedMethod
        : '',
      unPaged: true,
    };

    this._spinner.show();
    const rewards = this.rewardPointsService.exportAllProgressTarget(data);

    rewards.subscribe({
      next: (exportsDataRes: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = exportsDataRes;
          if (typeof exportsDataRes === 'string') {
            try {
              parsedResponse = JSON.parse(exportsDataRes);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(exportsDataRes);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let exportData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            exportData = JSON.parse(decrypted);
          } else {
            exportData = parsedResponse;
          }
          
          this.exportData = [];
          if (
            exportData &&
            exportData.content &&
            exportData.content.length
          ) {
            exportData.content.forEach((schemeInfo: any) => {
              const schemeInfoObj = {
                progressLeaderName: this.utility.toUpperCaseUtil(schemeInfo.name) ?? 'NA',
                progressRegion: schemeInfo.region ?? 'NA',
                progressZone: schemeInfo.zone ?? 'NA',

                progressCPMex: this.numberFormatService.formatNumber(schemeInfo.totalCropProtectionAmount),
                progressMainCrop: this.numberFormatService.formatNumber(schemeInfo.achievedCropProtectionAmount),
                progressAdvanceCrop: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalCropProtectionPercentage),

                progressNPPMex: this.numberFormatService.formatNumber(schemeInfo.totalNppAmount),
                progressNPPPurchases: this.numberFormatService.formatNumber(schemeInfo.achievedNppAmount),
                progressNPPAdvance: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalNppPercentage),

                progressTotalMex: this.numberFormatService.formatNumber(schemeInfo.totalTargetAmount),
                progressTotalPurchases: this.numberFormatService.formatNumber(schemeInfo.achievedTotalAmount) ?? 0,
                progressTotalAdvance: this.numberFormatService.formatPercentage(schemeInfo.achievedTotalPercentage),

                progressBonusCrop: this.numberFormatService.formatNumber(schemeInfo.cropProtectionBonus),
                progressNPPBonus: this.numberFormatService.formatNumber(schemeInfo.nppBonus),
                progressTotalBonus: this.numberFormatService.formatNumber(schemeInfo.totalBonus),

                progressTelephone: schemeInfo.mobileNo ?? 'NA',
                progressEmail: schemeInfo.email ?? 'NA',
                companyName: this.utility.toUpperCaseUtil(schemeInfo.company) ?? 'NA',
                progressCropsInTheArea: this.utility.capitalizeWords(schemeInfo.crop) ?? 'NA',

              };

              this.exportData.push(schemeInfoObj);
            });

            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: [
                'Leader Name',
                'Region',
                'Zone',

                'CP (Mex$)',
                'Achieved CP (Mex$)',
                'Achieved CP (%)',
                'NPP (Mex$)',
                'Achieved NPP (Mex$)',
                'Achieved NPP (%)',
                'Total Target (Mex$)',
                'Achieved Total (Mex$)',
                'Achieved Total (%)',

                'Bonus Crop',
                'NPP Bonus',
                'Total Bonus',

                'Telephone',
                'Email',
                'Company Name',
                'Main Crop',
              ],
            };

            new ngxCsv(
              this.exportData,
              this.isActiveTarget ? 'Target Details' : 'Target Progress',
              options
            );
          } else {
            this.toastr.warning('No data available');
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error('Failed to export data');
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message || 'Failed to export data');
          }
        } catch (e) {
          this.toastr.error('Failed to export data');
        }
      },
    });
  }
  exportActiveTargetData(page?: any) {
    let data = {
      pageLimit: this.perPage,
      currentPage: this.currentPage ? this.currentPage - 1 : 0,
      searchedValue: this.searchedString ? this.searchedString : '',
      startDate: this.fromDateAPI ? this.fromDateAPI : '',
      customerTypeId: this.isActiveTarget ? 1 : 2,
      scanningCategoryId: this.selectedSeasonDropdownData
        ? this.selectedSeasonDropdownData
        : 0,
      approvalStatus: this.selectedRedeemedMethod
        ? this.selectedRedeemedMethod
        : '',
      unPaged: true,
    };

    this._spinner.show();
    const rewards = this.rewardPointsService.exportAllActiveTarget(data);

    rewards.subscribe({
      next: (exportsDataRes: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = exportsDataRes;
          if (typeof exportsDataRes === 'string') {
            try {
              parsedResponse = JSON.parse(exportsDataRes);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(exportsDataRes);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let exportData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            exportData = JSON.parse(decrypted);
          } else {
            exportData = parsedResponse;
          }
          
          this.exportData = [];
          if (
            exportData &&
            exportData.content &&
            exportData.content.length
          ) {
            exportData.content.forEach((schemeInfo: any) => {
              const schemeInfoObj = {
                leaderName: this.utility.toUpperCaseUtil(schemeInfo.leaderName)
                  ? this.utility.toUpperCaseUtil(schemeInfo.leaderName)
                  : 'NA',
                Region: schemeInfo.region
                  ? schemeInfo.region
                  : 'NA',
                Zone: schemeInfo.zone
                  ? schemeInfo.zone
                  : 'NA',
                RFC: schemeInfo.rfc
                  ? schemeInfo.rfc
                  : 'NA',
                nppAmount: schemeInfo.nppAmount
                  ? this.numberFormatService.formatNumber(schemeInfo.nppAmount)
                  : 'NA',
                cropProtectionAmount: schemeInfo.cropProtectionAmount
                  ? this.numberFormatService.formatNumber(schemeInfo.cropProtectionAmount)
                  : 'NA',
                amount: schemeInfo.totalAmount
                  ? this.numberFormatService.formatNumber(schemeInfo.totalAmount)
                  : 'NA',
                approvedOn: schemeInfo.approvedOn ? moment(schemeInfo.approvedOn).format('DD-MM-YYYY') : 'NA',
                approvedBy: schemeInfo.approvedBy
                  ? schemeInfo.approvedBy
                  : 'NA',
                approvalStatus: schemeInfo.approvalStatus
                  ? schemeInfo.approvalStatus
                  : 'NA',
              };

              this.exportData.push(schemeInfoObj);
            });

            let options = {
              fieldSeparator: ',',
              quoteStrings: '"',
              decimalseparator: '.',
              showLabels: true,
              headers: [
                'Leader Name',
                'Region',
                'Zone',
                'RFC (Tax ID)',
                'CP (Mex$)',
                'NPP (Mex$)',
                'Total (Mex$)',
                'Approved on',
                'Approved by',
                'Status',
              ],
            };

            new ngxCsv(
              this.exportData,
              'Target Management Details',
              options
            );
          } else {
            this.toastr.warning('No data available');
          }
          this._spinner.hide();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error('Failed to export data');
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error)
        try {
          error = JSON.parse(error);
          if ('Full authentication is required to access this resource' == error.message) {
            localStorage.clear();
            this.router.navigate(['']);
            this.toastr.success('Signed Out Successfully');
          }
          else {
            this.toastr.error(error.message || 'Failed to export data');
          }
        } catch (e) {
          this.toastr.error('Failed to export data');
        }
      },
    });
  }
  /**
   * To handle the page change event
   * @param page
   */
  getPageData(page: any) {
    this.configurationSettings.currentPage = page;
    this.currentPage = page;
    if (this.isTargetProgress) {
      this.getAllTargetProgressData(this.currentPage);
    } else {
      this.getAllTargetData(this.currentPage);
    }
  }

  /**
   * To handle the page change event for progress tab
   * @param page
   */
  getProgressPageData(page: any) {
    this.configurationSettings.currentPage = page;
    this.currentPage = page;
    this.getAllTargetProgressData(this.currentPage);
  }

  /**
   * called when start date filter is changed
   * @param event
   */
  onStartDateChanged(event: any) {
    if (event && event.formatted) {
      this.startDate = '';
      this.endDate = '';
      this.endDateModel = '';
      this.startDate = moment(event.formatted, 'DD-MM-YYYY').format(
        'DD-MM-YYYY'
      );
      const momentDate = moment(this.startDate, 'DD-MM-YYYY').subtract(
        1,
        'day'
      );
    } else {
      this.startDate = '';
    }
  }
  startdate2: any;
  /**
   * called when end date filter is changed
   * @param event
   */
  onEndDateChanged(event: any) {
    if (event && event.formatted) {
      this.endDate = '';
      this.endDate = moment(event.formatted, 'DD-MM-YYYY').format('DD-MM-YYYY');
    } else {
      this.endDate = '';
    }
  }

  formatDate(date: Date): string {
    return date.toISOString();
  }
  /**
   * Method for getting the filtered data by the start date and end date
   */
  filterSchemeByDate(rangePicker: any) {
    this.fromDateAPI = moment(this.startDate, 'DD-MM-YYYY').format(
      'DD-MM-YYYY'
    );
    this.toDateAPI = moment(this.endDate, 'DD-MM-YYYY').format('DD-MM-YYYY');
  }

  /**
   * Called to clear the date filters
   */
  clearSchemeFilter() {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');

    if (this.isDateFilter) {
      this.isDateFilter = false;
      this.rewardsHistoryData = [];
      this.currentPage = 0;
      this.getAllTargetData();
    }
  }

  /**
   * Triggered when user clicks on the scanned and redeemed radio button
   */
  changeFilter(event: any) {
    this.startDate = '';
    this.endDate = '';
    this.startDateModel = '';
    this.endDateModel = '';
    this.fromDateAPI = '';
    this.toDateAPI = '';
    this.startDateAPI = '';
    this.endDateAPI = '';
    this.dateForm.controls['startDatePicker'].setValue('');
    this.dateForm.controls['endDatePicker'].setValue('');
    if (event.value) {
      if (this.isActiveTarget) {
        event.value === 'redeemed'
          ? this.rewardHistoryTab('isRedeemedDistributor')
          : this.rewardHistoryTab('isFcDistributor');
      } else if (this.isTargetProgress) {
        event.value === 'redeemed'
          ? this.rewardHistoryTab('isForagesRetailer')
          : this.rewardHistoryTab('isFcRetailer');
      }
    }
  }

  /**
   * Method to change the view between table and map
   * @param data
   */
  viewChange(data: any) {
    switch (data) {
      case 'isTable':
        this.isTable = true;
        this.isMap = false;
        break;
      case 'isMap':
        this.isTable = false;
        this.isMap = true;
    }
  }
  cleanCustomAndCallOnDate(val: any, event: any) {
    let month = event.getMonth() + 1;
    let day = event.getDate();
    const year = event.getFullYear();
    if (day < 10) {
      day = '0' + day;
    }
    if (month < 10) {
      month = '0' + month;
    }
    const date = day + '-' + month + '-' + year;
    switch (val) {
      case 'from-date':
        this.fromDateAPI = date;
        break;
      case 'to-date':
        this.toDateAPI = date;
        break;
    }

    this.startDateAPI = this.fromDateAPI;
    this.endDateAPI = this.toDateAPI;
    const today = new Date();
    this.minDate = today;
    this.maxDate = today;
  }

  dateValidation(fromDateVal: any, toDateVal: any) {
    const fromDate = fromDateVal === undefined ? '' : fromDateVal.split('-');
    let toDate = toDateVal === undefined ? '' : toDateVal.split('-');
    const fromDateValue = fromDate[0] + fromDate[1] + fromDate[2];
    const toDateValue = toDate[0] + toDate[1] + toDate[2];
    if (fromDateValue > toDateValue) {
      this.toastr.error(
        'Invalid date. Start Date should be less than End Date'
      );
      return;
    } else {
      this.startDate = this.fromDateAPI;
      this.endDate = this.toDateAPI;
    }
  }
  funRestEmail(event: any) {
    var k;
    k = event.charCode;
    if (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      event.key == '' ||
      event.keyCode == 13
    ) {
    } else {
      this.toastr.error(event.key + ' ' + 'not allowed');
    }

    return (
      (k > 64 && k < 91) ||
      (k > 96 && k < 123) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57) ||
      event.key == '.' ||
      event.key == '-' ||
      event.key == '_' ||
      event.key == '+' ||
      event.key == '@' ||
      (event.key == '' && event.key !== 'Enter')
    );
  }

  funRestSearchPrevent(event: any) {
    if (event.target.selectionStart === 0 && event.code === 'Space') {
      event.preventDefault();
    }
    if (event.target.value.substr(-1) === ' ' && event.code === 'Space') {
      event.preventDefault();
      // this.toastr.error('Double space is not allowed');
    }

    var k = event.charCode || event.keyCode;
    if (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    ) {
      // Allow uppercase letters, lowercase letters, backspace, space, and numbers
    } else {
      event.preventDefault();
      // this.toastr.error(event.key + ' ' + 'not allowed');
    }

    return (
      (k >= 65 && k <= 90) ||
      (k >= 97 && k <= 122) ||
      k == 8 ||
      k == 32 ||
      (k >= 48 && k <= 57)
    );
  }

  @ViewChild('approveConfirmDialog') approveConfirmDialog!: TemplateRef<any>;
  selectedData: any;

  approveTarget(data: any) {
    this.selectedData = data;
    this.dialog.open(this.approveConfirmDialog, {
      width: '400px',
      disableClose: false,
      panelClass: 'confirm-dialog-container',
      hasBackdrop: true,
      autoFocus: false,
      restoreFocus: true,
    });
  }

  confirmApproval() {
    this._spinner.show();
    this.dialog.closeAll();
    if (!this.selectedData || (!this.selectedData.targetId && !this.selectedData.leaderId)) {
      this._spinner.hide();
      this.toastr.error("Missing required data for approval");
      return;
    }
    const approvalPayload = {
      leaderId: this.selectedData.targetId || this.selectedData.leaderId, // Use targetId first, then leaderId
      amount: Number(this.selectedData.amount || this.selectedData.totalAmount),
      taxId: this.selectedData.taxId || this.selectedData.rfc,
      startDate: this.selectedData.startDate,
      endDate: this.selectedData.endDate,
      isActive: true,
      grower: this.selectedData.grower || false,
      cropProtectionAmount: Number(this.selectedData.cropProtectionAmount) || 0,
      nppAmount: Number(this.selectedData.nppAmount) || 0
    };

    this.rewardPointsService.approveTarget(approvalPayload).subscribe({
      next: (response: any) => {
        try {
          // Check if response needs decryption
          if (typeof response === 'string' && response) {
            try {
              const decrypted = this.utility.decrypt(response);
              response = JSON.parse(decrypted);
            } catch (e) {
              // If decryption fails, use original response
            }
          }
          
          this._spinner.hide();
          this.toastr.success("Target approved successfully");
          this.fixToastVisibility();
          this.getAllTargetData();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to approve target");
          this.fixToastVisibility();
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error);
        try {
          error = JSON.parse(error);
          this.toastr.error(error.message || "Failed to approve target");
          this.fixToastVisibility();
        } catch (e) {
          this.toastr.error("Failed to approve target");
          this.fixToastVisibility();
        }
      }
    });
  }

  rejectTargetId: number | null = null;
  rejectRemark: string = '';
  rejectDialogRef!: MatDialogRef<any>;
  @ViewChild('rejectDialog') rejectDialog!: TemplateRef<any>;

  openRejectDialog(data: any) {
    this.rejectTargetId = data.id || data.leaderId;
    this.rejectRemark = '';

    this.rejectDialogRef = this.dialog.open(this.rejectDialog, {
      width: '400px',
      disableClose: false,
      panelClass: 'reject-dialog-container',
      hasBackdrop: true,
    });
  }

  submitReject() {
    if (!this.rejectTargetId || !this.rejectRemark.trim()) {
      this.toastr.warning('Please enter a comment');
      return;
    }

    this._spinner.show();
    this.rewardPointsService.rejectTarget(this.rejectTargetId, this.rejectRemark).subscribe({
      next: (response: any) => {
        try {
          // Check if response needs decryption
          if (typeof response === 'string' && response) {
            try {
              const decrypted = this.utility.decrypt(response);
              response = JSON.parse(decrypted);
            } catch (e) {
              // If decryption fails, use original response
            }
          }
          
          this._spinner.hide();
          this.toastr.success("Target rejected successfully");
          this.fixToastVisibility();
          this.rejectDialogRef.close();
          this.getAllTargetData();
        } catch (error) {
          console.error('Error processing response:', error);
          this._spinner.hide();
          this.toastr.error("Failed to reject target");
          this.fixToastVisibility();
        }
      },
      error: (errorResponse: any) => {
        this._spinner.hide();
        let error: any = (errorResponse.error);
        try {
          error = JSON.parse(error);
          this.toastr.error(error.message || "Failed to reject target");
          this.fixToastVisibility();
        } catch (e) {
          this.toastr.error("Failed to reject target");
          this.fixToastVisibility();
        }
      }
    });
  }

  cancelReject() {
    this.rejectDialogRef.close();
  }
  private fixToastVisibility(): void {
    this.toastFixService.fixToastVisibility();
  }

  selectRegion(event: any) {
    this.selectedRegion = event.id;

    // Clear all below Region
    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';

    this.getZoneByID(this.selectedRegion);
  }

  deselectionRegion(event: any) {

    this.selectedRegion = [];

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }

  deselectionAllRegion(event: any) {
    this.deselectionRegion(event);

    this.selectedZone = [];
    this.zoneDataList = [];
    this.zoneValue = '';
  }

  // ================== ZONE ==================
  selectZone(event: any) {
    this.selectedZone = event.id;
  }

  deselectionZone(event: any) {
    this.selectedZone = [];
  }

  deselectionAllZone(event: any) {
    this.deselectionZone(event);
  }

  clearFilterProgress() {
    this._spinner.show();
    this.selectedRegion = [];
    this.regionValue = '';
    this.selectedZone = [];
    this.zoneValue = '';
    this.getAllTargetProgressData()
    this.filterMenuDailogProgressRef.close();
  }
  filterApplyProgress() {
    this._spinner.show(); 
    this.getAllTargetProgressData();
    this.filterMenuDailogProgressRef.close();
  }
  getZoneByID(id: any) {
    const data = {
      zoneId: id
    };
    this.zoneDataList = [];
    this.userService.getZoneById(data).subscribe(
      (response: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(response);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let zoneData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            zoneData = JSON.parse(decrypted);
          } else {
            zoneData = parsedResponse;
          }
          
          this.zoneDataList = zoneData.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error processing zone response:', error);
          this.toastr.error("Failed to load zone data");
        }
      },
      (error) => {
        console.error('Failed to fetch zone data', error);
        this.toastr.error("Failed to load zone data");
      }
    );
  }

  regionDropdownSettings = {
    text: 'Select Region',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };
  zoneDropdownSettings = {
    text: 'Select Zone',
    enableSearchFilter: true,
    classes: 'myclass customer-dd ',
    labelKey: 'name',
    enableFilterSelectAll: false,
    singleSelection: true,
    maxHeight: 150,
    disabled: false,
    autoPosition: false,
    badgeShowLimit: 1,
    showCheckbox: false,
  };

  getRegion() {
    this.regionDataList = [];
    this.userService.getRegion().subscribe(
      (response: any) => {
        try {
          // First, check if response is a string and try to parse it
          let parsedResponse = response;
          if (typeof response === 'string') {
            try {
              parsedResponse = JSON.parse(response);
            } catch (e) {
              // If parsing fails, try to decrypt first
              const decrypted = this.utility.decrypt(response);
              parsedResponse = JSON.parse(decrypted);
            }
          }
          
          // Check if response has encryptedBody property
          let regionData;
          if (parsedResponse && parsedResponse.encryptedBody) {
            // Decrypt the encryptedBody
            const decrypted = this.utility.decrypt(parsedResponse.encryptedBody);
            regionData = JSON.parse(decrypted);
          } else {
            regionData = parsedResponse;
          }
          
          this.regionDataList = regionData.map((item: any) => ({
            id: item.id,
            name: item.name,
            code: item.code,
          }));
        } catch (error) {
          console.error('Error processing region response:', error);
          this.toastr.error("Failed to load region data");
        }
      },
      (error) => {
        console.error('Failed to fetch region data', error);
        this.toastr.error("Failed to load region data");
      }
    );
  }
}
