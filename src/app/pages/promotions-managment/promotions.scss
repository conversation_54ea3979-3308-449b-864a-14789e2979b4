@import "../../theme/sass/_auth";
@import "../../../styles.scss";
@import "../../theme/sass/mixins";
@import "../../theme/sass/conf/variables.scss";

// Essential mixins and variables
@include scrollbars(.5em, #d9d9d9, rgba(0, 0, 0, 0));
$font-size: 13px;

// Core utility classes
.width-100 {
  width: 100%;
}

.width-60 {
  width: 60%;
}

.width-40 {
  width: 40%;
}

// Error message styling
.error-message {
  .help-block {
    color: red;
  }
}

// Button styling
.submit-color {
  background-color: #FF8033;
}

// Main container structure
.promotions-container {
  width: 100%;
  overflow-y: hidden;
  padding-left: 14px;
  margin-right: 14px;

  .promotions-grid-container {
    width: 98%;
    float: left;
    border-radius: $border-radius;
    margin-top: 60px;
    position: relative;

    .promotions-grid-data-parent-container {
      min-height: 485px;
      background: #ffffff;
      overflow-y: auto;
    }

    .promotions-grid-data-container {
      width: 100%;
      overflow-y: auto;
      background: #fff;
      border-radius: 2px;
      padding: 10px 15px 25px;

      .area-filter-container {
        display: flex;
        height: 65px;

        .left-column {
          width: 30%;

          .main-campaign {
            padding-top: 18px;

            .panel {
              .panel-body {
                .wizard {
                  min-width: 100%;
                  float: left;
                  background: #fff;
                  margin: 0;
                  margin-top: -18px;
                  font-size: 14px;

                  @media screen and (max-width: 500px) {
                    font-size: 8px;
                  }

                  @media screen and (max-width: 768px) {
                    font-size: 12px;
                  }

                  .profile-tab {
                    float: left;
                    text-align: center;
                    border-bottom: 3px solid #c6c6c6;
                    height: 50px;
                    line-height: 50px;
                    text-decoration: none;
                    cursor: pointer;
                    font-size: 14px;
                    color: #c6c6c6;
                    width: 33%;
                    font-weight: 500;

                    &:last-child {
                      border-right: none;
                    }

                    i {
                      margin-left: 5px;
                    }

                    img {
                      height: 20px;
                      margin-left: 5px;
                    }
                  }

                  .active {
                    border-bottom: 3px solid $button-color;
                    color: #FF8033;

                    img {
                      height: 24px;
                      margin-left: 5px;
                    }
                  }
                }
              }
            }
          }
        }

        // Right column with search and filters
        .right-column {
          padding-top: 15px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;

          .search-container {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: flex-end;

            // Button styling
            .export-button button,
            .add-button button {
              width: 38px;
              height: 38px;
              border-radius: 0.25rem;
              border: 1px solid #FF8033;
              background: #FF8033;
              color: #fff;
            }

            .input-group {
              display: flex;
              justify-content: flex-end;
              margin-bottom: 10px;

              // Search input styling
              .search-input {
                display: flex;
                align-items: center;
                width: 35%;

                .input-group-add {
                  padding: 0.55rem 0.75rem;
                  margin-bottom: 0;
                  font-size: 0.9rem;
                  font-weight: 400;
                  line-height: 1;
                  color: #464a4c;
                  text-align: center;
                  background-color: #fff;
                  border: 1px solid #ccc;
                  border-radius: 0.25rem;
                  width: 85%;
                  margin-left: 11%;
                  display: flex;

                  img {
                    height: 18px;
                    cursor: pointer;
                  }

                  i {
                    float: left;
                    margin-top: 2px;
                  }

                  input {
                    border: none;
                    width: 85%;
                    outline: none;
                  }
                }
              }

              .export-button,
              .add-button {
                margin-right: 10px;
                // margin-top: 10px;
              }

              .add-btn-container {
                display: flex;
                justify-content: flex-end;
                /* Align to the right */
                height: 40px;
                margin: 0;

                .create-level-btn {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  gap: 8px;
                  height: 37px;
                  background-color: #ff8033;
                  border-radius: 0.25rem;
                  border: 1px solid #ff8033;
                  color: #fff;
                  padding: 0 14px;
                  font-size: 14px;
                  font-weight: 500;
                  cursor: pointer;
                  transition: background-color 0.2s ease;

                  &:hover {
                    background-color: darken(#ff8033, 5%);
                  }

                  img {
                    display: inline-block;
                    vertical-align: middle;
                  }

                  span {
                    display: inline-block;
                    vertical-align: middle;
                    white-space: nowrap;
                  }
                }
              }
            }
          }

          // Tablet responsiveness
          @media screen and (max-width: 768px) {
            .search-container {
              justify-content: center;
            }
          }
        }

        // Table styling
        .promotions-table {
          font-size: 15px;
          min-width: 100%;
          overflow-y: hidden;
        }
      }
    }
  }

  // Toggle button styling
  .mat-button-toggle-group {
    display: inline-flex;
    flex-direction: row;
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    outline: none;
    flex-wrap: wrap;
    margin: 0;
  }

  .toggle-group .mat-button-toggle {
    vertical-align: middle !important;
    height: 50px;
    margin: 0;
    padding: 0;
  }

  .mat-button-toggle {
    color: black;
    border: 1px #666666;
    font-weight: 600;
    height: 0;
    width: 250px;
  }

  .mat-button-toggle-checked {
    background-color: #FF8033 !important;
    color: white !important;
    height: 0;
    width: 250px !important;
  }
}

// promotions dialog styling
.promotions-dialog-scrollable {
  max-height: 90vh; // Adjust as needed (60vh–80vh usually works well)
  overflow-y: auto;
  padding-right: 10px; // For spacing from scrollbar

  .main-promotions-container {
    padding: 20px;

    .heading-container {
      color: #FF8033;
      font-family: $sans-font-family;

      h3 {
        font-weight: 800;
      }
    }

    // Form input styling
    .form-input1-container {
      display: grid;
      grid-template-columns: repeat(3, 34%);
      gap: 10px;
      width: 97%;
      outline: none;
      padding-bottom: 10px;

      span {
        .input-field {
          font-weight: 400;
          line-height: 1;
          color: #464a4c;
          padding-left: 10px;
          background-color: #fff;
          border: 1px solid #ccc !important;
          border-radius: 0.25rem;
          height: 38px;
          width: 100%;
        }

        input:focus,
        input:focus-visible {
          outline: none;
        }
      }
    }

    // Textarea styling
    .mat-form {
      textarea {
        width: 100%;
        height: 80px;
        resize: none;
        border: 1px solid #ccc;
        border-radius: 3px;
        padding-left: 10px;
      }

      .error-message {
        color: red;
      }

      .help-block {
        color: #fd0d0d;
        vertical-align: sub;
        margin: 0 0 0 40px;
      }
    }

    // Button container styling
    .button-container {
      display: flex;
      justify-content: center;
      gap: 20px;

      button {
        height: 40px;
        width: 30%;
        border: none;
        border-radius: 5px;
      }

      .btn-cancel {
        background-color: #fff;
        color: #FF8033;
        font-family: sans-serif;
        font-weight: 600;
        border: 1px solid #FF8033;
      }

      .btn-submit {
        background-color: #FF8033;
        color: #fff;
        font-family: $sans-font-family;
        font-weight: 600;
      }
    }
  }
}

@media screen and (min-width: 200px) and (max-width: 575px) {
  .promotions-container .promotions-grid-container {
    .promotions-grid-action {
      .promotions-grid-search-container {
        width: 100%;

        .promotions-grid-search-input {
          width: 100%;
        }
      }

      .promotions-grid-action-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 10px;

        .promotions-grid-action-add {
          width: 60%;
          margin-top: 5px;
        }
      }
    }
  }
}

.disable-submit {
  opacity: 0.6;
  cursor: not-allowed;

  &:hover {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.input-group {
  flex-wrap: unset !important;
}

// Product view popup styling
.common-popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .popup-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }

  .popup-content {
    position: relative;
    z-index: 2;
    background: white;
    border-radius: 8px;
    width: 60%;
    max-width: 800px;
    max-height: 85vh;
    overflow: auto;
    /* ✅ Enable scroll on overflow */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);

    .popup-header {
      padding-top: 10px;
      align-items: left;
      text-align: left;

    }

    .table-container {
      padding: 0px 20px;

    }

    .popup-footer-product {
      padding: 20px;
      display: flex;
      justify-content: center;

      .product-cancel-btn {
        background-color: #FF8033;
        color: white;
        border: none;
        width: 130px;
        padding: 10px 35px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: darken(#FF8033, 10%);
        }
      }
    }
  }

  // Responsive design
  @media screen and (max-width: 768px) {
    .popup-content {
      width: 90%;
      max-height: 90vh;
    }
  }
}

// Product Details Modal Styling
.product-details-modal {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 900px;
  width: 100%;
  max-height: 80vh;

  .modal-header {
    padding: 20px 30px;
    background-color: white;
    text-align: center;

    .modal-title {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
      font-family: $sans-font-family;
    }
  }

  .modal-content {
    padding: 0 30px;
    max-height: 60vh;
    overflow-y: auto;

    .product-table-wrapper {
      .product-table {
        width: 100%;
        border-collapse: collapse;
        font-family: $sans-font-family;

        .table-header {
          background-color: #f5f5f5;

          th {
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #333;
            font-size: 14px;
            border: none;
          }
        }

        .table-row {
          &:nth-child(even) {
            background-color: #f9f9f9;
          }

          td {
            padding: 15px 12px;
            font-size: 13px;
            color: #555;
            border: none;
            vertical-align: middle;

            &.product-name {
              color: #007bff;
              font-weight: 500;
            }
          }

          &:hover {
            background-color: #f0f0f0;
          }
        }
      }
    }
  }

  .modal-footer {
    padding: 20px 30px;
    background-color: white;
    text-align: center;

    .close-button {
      background-color: #FF8033;
      color: white;
      border: none;
      padding: 10px 30px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      font-family: $sans-font-family;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: darken(#FF8033, 10%);
      }
    }
  }
}

// Add/Edit Promotion Popup Styling
.add-promotion-popup {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-height: 80vh;
  overflow-y: auto;

  .popup-header {
    padding: 20px 30px;
    background-color: white;
    text-align: left;

    h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .popup-content {
    padding: 0 30px 20px 30px;

    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      &.additional-product-row {
        border-top: 1px solid #f0f0f0;
        padding-top: 20px;
        margin-top: 20px;
      }

      .form-group {
        flex: 1;

        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 520;
          color: #333;
          font-size: 14px;

          .required {
            color: #dc3545;
            margin-left: 2px;
          }
        }

        /* Form input styling */
        .form-input {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          transition: border-color 0.2s ease;
          height: 40px;
          box-sizing: border-box;
          background-color: white;
          padding-right: 40px;

          &:focus {
            outline: none;
            border-color: #FF8033;
            box-shadow: 0 0 0 1px rgba(255, 128, 51, 0.2);
          }

          &.ng-invalid.ng-touched {
            border-color: #dc3545;
          }
        }

        /* Date picker container */
        .date-picker {
          position: relative;

          .mat-datepicker-toggle {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: #FF8033;

            .mat-icon {
              font-size: 20px;
              opacity: 0.9;
              width: 20px;
              height: 20px;
            }
          }
        }

        /* Calendar styling */
        .mat-datepicker-content {
          .mat-calendar-body-selected {
            background-color: #FF6B00 !important;
            color: white !important;
          }

          .mat-calendar-body-today:not(.mat-calendar-body-selected) {
            border-color: #FF6B00;
          }
        }

        /* Hide native date picker icon */
        input[matDatepicker]::-webkit-calendar-picker-indicator {
          display: none;
        }
      }
    }

    .file-upload-wrapper {
      position: relative;
      width: 260px; // Ensure it takes full width of its parent
      max-width: 260px; // Set a maximum width (adjust as needed)

      .file-input {
        position: absolute;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
      }

      .file-upload-display {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
        transition: border-color 0.2s ease;
        height: 40px;
        box-sizing: border-box;
        width: 100%; // Ensure it takes full width of the wrapper
        overflow: hidden; // Prevent content from expanding the container

        &:hover {
          border-color: #FF8033;
        }

        .upload-text {
          font-size: 14px;
          color: #666;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          min-width: 0; // Important for flex item text truncation
        }

        .upload-icon {
          color: #FF8033;
          font-size: 18px;
          margin-left: 8px;
          flex-shrink: 0; // Prevent the icon from shrinking
        }
      }
    }

    .add-more-section {
      text-align: right;
      position: relative;
      top: -18px;

      .add-more-btn {
        background: none;
        border: none;
        color: #FF8033;
        padding: 0;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          color: darken(#FF8033, 10%);
          text-decoration: underline;
        }

        &:disabled {
          color: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }

  .popup-footer {
    padding: 20px 30px;
    background-color: white;
    display: flex;
    justify-content: center;
    gap: 20px;

    .cancel-btn {
      background-color: transparent;
      color: #666;
      border: 1px solid #ddd;
      padding: 10px 30px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #999;
        color: #333;
      }
    }

    .submit-btn {
      background-color: #FF8033;
      color: white;
      border: none;
      padding: 10px 30px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background-color: darken(#FF8033, 10%);
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }

  // Product row delete button styling - 85% inputs, 15% delete (prevent wrapping)
  .product-row {
    display: flex;
    align-items: center; // Center align all items vertically
    gap: 15px;
    flex-wrap: nowrap; // Prevent items from wrapping to next line

    // Default: When NO delete button - inputs take full width (50% each)
    .form-group:not(.delete-button-group) {
      flex: 1; // Take equal space when no delete button
      min-width: 0; // Allow flex items to shrink below their content size
    }

    // When delete button IS present - inputs take 85% total
    &:has(.delete-button-group) {
      .form-group:not(.delete-button-group) {
        flex: 0 0 42.5%; // Each input takes 42.5% (85% total for both inputs)
        min-width: 0; // Allow shrinking if needed
      }
    }

    // Delete button section takes 15% but with minimum constraints
    .delete-button-group {
      flex: 0 0 15%;
      min-width: 50px; // Ensure button remains clickable
      max-width: 80px; // Don't let it get too wide
      display: flex;
      align-items: center; // Center the button vertically
      justify-content: center; // Center the button horizontally
      margin-bottom: 0; // Remove any bottom margin

      .delete-row-btn {
        background: none;
        border: 1px solid #FF8033;
        border-radius: 4px;
        color: #FF8033;
        cursor: pointer;
        margin-bottom: 7px;
        height: 36px;
        width: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        flex-shrink: 0; // Don't let the button shrink

        &:hover {
          background-color: #FF8033;
          color: white;
        }

        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(255, 128, 51, 0.2);
        }

        i {
          font-size: 14px;
        }
      }
    }
  }

  // Fallback for browsers that don't support :has() selector
  .product-row.has-delete-button {
    .form-group:not(.delete-button-group) {
      flex: 0 0 42.5%; // Each input takes 42.5% (85% total for both inputs)
      min-width: 0; // Allow shrinking if needed
    }
  }

  /* Multiselect Custom Styles */
  .multiselect-custom {
    width: 100%;
    margin-top: 8px;

    .dropdown-btn {
      padding: 10px 12px;
      border: 1px solid #ddd !important;
      border-radius: 4px !important;
      font-size: 14px;
      height: 40px;

      .selected-item {
        border: none !important;
        background: #f5f5f5 !important;
        color: #333 !important;
        margin-right: 4px !important;
        padding: 2px 10px !important;
      }

      .dropdown-down {
        border-top: 6px solid #666 !important;
        border-left: 6px solid transparent !important;
        border-right: 6px solid transparent !important;
      }

      .dropdown-up {
        border-bottom: 6px solid #666 !important;
        border-left: 6px solid transparent !important;
        border-right: 6px solid transparent !important;
      }
    }

    .dropdown-list {
      border: 1px solid #ddd !important;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;

      .filter-textbox {
        border-bottom: 1px solid #ddd !important;
        padding: 8px !important;

        input {
          width: 100% !important;
          padding: 8px !important;
          border: 1px solid #ddd !important;
          border-radius: 4px !important;
        }
      }

      .item1 {
        padding: 8px 10px !important;

        &:hover {
          background-color: #f5f5f5 !important;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    max-width: 95%;

    .popup-content {
      padding: 0 20px 20px 20px;

      .form-row {
        flex-direction: column;
        gap: 15px;
      }
    }
  }
}
.product-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  .mainsection {
    flex: 1 1 80%;
    display: flex;
    gap: 16px;

    .form-group {
      display: flex;
      flex-direction: column;
      flex: 1;
    }
  }

  .delete-button-group {
    flex: 0 0 auto;
    padding-top: 22px;
    display: flex;
    align-items: flex-start;

    .delete-row-btn {
      background: none;
      border: 1px solid #FF8033;
      border-radius: 4px;
      color: #FF8033;
      cursor: pointer;
      height: 32px;
      width: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      padding: 0;
    }
  }
}


// Confirm Dialog Styling (for Approve - matching target component exactly)
.confirm-dialog {
  background: white;
  border-radius: 8px;
  padding: 30px;
  max-width: 400px;
  width: 100%;
  text-align: center;

  .image-container {
    margin-bottom: 25px;

    img {
      width: 80px;
      height: auto;
    }
  }

  .confirm-content {
    margin-bottom: 30px;

    .text-fields {
      font-size: 18px;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
    }
  }

  .action-button-section {
    display: flex;
    justify-content: center;
    gap: 15px;

    .action-btn {
      padding: 12px 30px;
      border-radius: 6px;
      font-weight: 600;
      cursor: pointer;
      border: none;
      font-size: 16px;
      min-width: 80px;

      &.submit-btn {
        background-color: #FF8033;
        color: white;

        &:hover {
          background-color: #e06c2b;
        }
      }

      &.cancel-btn {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        color: #6c757d;

        &:hover {
          background-color: #e9ecef;
        }
      }
    }
  }
}

// Reject Dialog Styling (matching invoice component)
.reject-dialog {
  background: white;
  border-radius: 8px;
  padding: 25px;
  max-width: 450px;
  width: 100%;

  .reject-title {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    font-family: $sans-font-family;
    text-align: center;
  }

  .reject-content {
    margin-bottom: 25px;

    .reject-textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: $sans-font-family;
      resize: vertical;
      min-height: 100px;
      transition: border-color 0.2s ease;
      box-sizing: border-box;

      &:focus {
        outline: none;
        border-color: #FF8033;
        box-shadow: 0 0 0 1px rgba(255, 128, 51, 0.2);
      }

      &::placeholder {
        color: #999;
      }
    }
  }

  .reject-actions {
    display: flex;
    justify-content: center;
    gap: 15px;

    .btn-cancel {
      background-color: #6c757d;
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-family: $sans-font-family;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: darken(#6c757d, 10%);
      }
    }

    .btn-submit {
      background-color: #FF8033;
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
      font-family: $sans-font-family;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background-color: darken(#FF8033, 10%);
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }
  }
}

/* Make sure the dialog appears with proper styling */
::ng-deep .confirm-dialog-container {
  .mat-dialog-container {
    padding: 0 !important;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  /* Ensure button styles are applied with high specificity */
  .confirm-dialog .action-button-section .action-btn {
    padding: 12px 30px !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    border: none !important;
    font-size: 16px !important;
    min-width: 80px !important;

    &.submit-btn {
      background-color: #FF8033 !important;
      color: white !important;

      &:hover {
        background-color: #e06c2b !important;
      }
    }

    &.cancel-btn {
      background-color: #f8f9fa !important;
      border: 1px solid #dee2e6 !important;
      color: #6c757d !important;

      &:hover {
        background-color: #e9ecef !important;
      }
    }
  }
}