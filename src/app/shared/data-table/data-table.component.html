<div class="grid-container">
  <div class="custom-data-table">
    <div class="custyle">
      <table [ngClass]="{ 'font-size-11': path == '/product-details' }" class="table-striped custab">
        <thead class="table-head">
          <tr [ngClass]="{ 'bg-color': path == '/product-details' }">
            <ng-container *ngFor="let tableHead of tableHeads; let i = index">
              <th [ngClass]="{
                    'fixed-min-width': path != '/product-details',
                    'table-width-custom': path == '/product-details',
                    'min-width-15': (tableHead == 'Zone Code' && path == '/zones')
                  }" 
                  class="border-row head-row fixed-min-width"
                  [class.sortable]="i < tableColName.length && tableHead !== tableConfiguration.actionsColumnName && tableHead !== 'View/Download' && tableHead !== 'View Doc.' && tableHead !== 'Details'"
                  (click)="i < tableColName.length && tableHead !== tableConfiguration.actionsColumnName && tableHead !== 'View/Download' && tableHead !== 'View Doc.' && tableHead !== 'Details' ? sortData(tableColName[i]) : null">
                <div class="header-content">
                  {{ tableHead }}
                  <i *ngIf="sortColumn === tableColName[i] && tableHead !== tableConfiguration.actionsColumnName && tableHead !== 'View/Download' && tableHead !== 'View Doc.' && tableHead !== 'Details'" 
                     class="fa" 
                     [ngClass]="{'fa-sort-asc': sortDirection === 'asc', 'fa-sort-desc': sortDirection === 'desc'}">
                  </i>
                </div>
              </th>
            </ng-container>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="!tableData || tableData.length === 0" class="center no-data">
            <td [attr.colspan]="tableHeads.length" class="no-data-message">
              {{ tableConfiguration.noDataMessage }}
            </td>
          </tr>
          <ng-container *ngIf="tableData && tableData.length > 0">
            <ng-container *ngFor="let data of tableData; let i = index">
              <tr [ngClass]="{
                  'bg-color-white': path == '/product-details',
                  'font-size-11': path == '/product-details'
                }" class="table-row">
                <ng-container *ngFor="let colName of tableColNameGenerated">
                  <td [attr.title]="
                      path == '/product-details' ? data['' + colName] : ''
                    " [ngStyle]="{
                    'color': (path === '/invoices' && colName === 'status')||(path === '/users' && colName === 'agreement_status') || (path === '/users' && colName === 'status')||(path === '/target-management' && colName === 'approvalStatus')
                      ? getStatusColor(data[colName]) 
                      : ''
                  }" [ngClass]="{
                      'text-right':
                        colName == 'percentage_growth' ||
                        (path == '/product-details' &&
                          (colName == 'tm_budget_volume' ||
                            colName == 'apr' ||
                            colName == 'may' ||
                            colName == 'jun' ||
                            colName == 'jul' ||
                            colName == 'aug' ||
                            colName == 'sep' ||
                            colName == 'oct' ||
                            colName == 'nov' ||
                            colName == 'dec' ||
                            colName == 'jan' ||
                            colName == 'feb' ||
                            colName == 'mar')),
                      'text-left': colName != 'percentage_growth',
                      'overflow-property-table': path == '/product-details'
                    }" class="border-row table-header" (click)="colName == 'sku_code' ? colData($event) : ''">
                    <ng-container
                      *ngIf="(path === '/invoices' && colName === 'status')||(path === '/users' && colName === 'agreement_status')||(path === '/target-management' && colName === 'approvalStatus')">
                      <span class="status-dot" [ngStyle]="{
                    'background-color': getStatusColor(data[colName])}"></span>
                    </ng-container>
                    <!-- <ng-container *ngIf="colName === 'view_doc'; else regularContent">
                    <img [src]="data[colName]" alt="View Document" width="36" height="36">
                  </ng-container> -->
                    <ng-container *ngIf="colName === 'view_doc' || colName === 'products' || colName === 'product'; else regularContent">
                      <img class="view-pdf-icon" [src]="data[colName]" (click)="viewPDFData(data, colName)" alt="View Document"  [title]="colName === 'view_doc' ? 'View Documents' : (colName === 'product' ? 'View Details' : 'Product Details')"
                        width="24px" height="24px">
                    </ng-container>

                    <ng-template #regularContent>
                      <span *ngIf="showIndex?.index !== i">{{ data[colName] !== null && data[colName] !== undefined ?
                        data[colName] : 'NA' }}</span>
                      <input *ngIf="tableConfiguration.editBox && showIndex?.index === i" class="form-control"
                        [ngClass]="{ 'input-box-skus': colName == 'sku' }" [value]="data[colName]"
                        (change)="valueCol($event, colName)" [readonly]="colName == 'sku'" />
                    </ng-template>
                  </td>
                </ng-container>
                <!-- View/Download column -->
                <td class="border-row fixed-min-width" *ngIf="tableConfiguration.showViewDownload">
                  <span class="add-edit" title="View" (click)="onViewClick(data)">
                    <img src="/assets/img/viewDoc.svg" alt="View Icon" class="add-edit">
                  </span>
                  <span class="add-edit" title="Download" (click)="onDownloadClick(data)">
                    <img src="../../../assets/img/Download_btn.svg" alt="Download Icon" class="add-edit">
                  </span>
                </td>
                <td class="border-row fixed-min-width" [ngClass]="{'actions-column': activeTabTitle === 'Leaders'}"
                  *ngIf="tableConfiguration.showActionsColumn">
                  <span *ngIf="tableConfiguration.productIcon" title="Brand Details" (click)="productDetails(data)"
                    class="add-edit-user">
                  </span>

                  <ng-container *ngIf="i != showIndex.index && tableConfiguration.showEdit">
                    <span
                      *ngIf="(!path.includes('/product-catalog') && !path.includes('/invoices') && !path.includes('/promotions-management')) || (path.includes('product-catalog') && isAdmin)"
                      [attr.title]="typeof tableConfiguration.showEdit !== 'string' ? 'Edit' : null"
                      (click)="editRowData(data, i)" class="add-edit-user">
                      <img *ngIf="typeof tableConfiguration.showEdit !== 'string'"
                        src="../../../assets/img/edit_orange.png" alt="edit-icon" width="18px" height="20px" />
                      <img *ngIf="typeof data.showEdit === 'string'" [src]="data.showEdit" alt="Status" width="36px"
                        height="36px" class="status-icon" (click)="toggleEditImage(i, $event)">
                    </span>
                  </ng-container>

                  <!-- Edit button for invoices and promotions-management -->
                  <ng-container *ngIf="(i != showIndex.index && tableConfiguration.showEdit) && (path === '/invoices' || path === '/promotions-management')">
                    <span class="add-edit-user"
                      [class.disabled]="data.status === 'Approved' || data.status === 'Rejected'"
                      (click)="(data.status !== 'Approved' && data.status !== 'Rejected') && editRowData(data, i); $event.stopPropagation()">
                      <img *ngIf="typeof tableConfiguration.showEdit !== 'string'"
                        [matTooltip]="path === '/invoices' ? 'Edit Invoice' : 'Edit Promotion'"
                        matTooltipClass="large-tooltip" [matTooltipClass]="'tooltip-style'"
                        src="../../../assets/img/edit_orange.png" alt="Edit Icon" width="18px" height="20px" />
                    </span>
                  </ng-container>
                  <!-- Approve button for invoices and promotions-management -->
                  <ng-container *ngIf="(i != showIndex.index && tableConfiguration.showApprove) && (path === '/invoices' || path === '/promotions-management')">

                    <span class="add-edit-user"
                      [class.disabled]="data.status === 'Approved' || data.status === 'Rejected' || data.isAssigned === false"
                      (click)="(data.status !== 'Approved' && data.status !== 'Rejected' && data.isAssigned !== false) && onApprove(data); $event.stopPropagation()">

                      <img *ngIf="typeof tableConfiguration.showEdit !== 'string'"
                        [matTooltip]="path === '/invoices' ? 'Approve Invoice' : 'Approve Promotion'"
                        matTooltipClass="large-tooltip" [matTooltipClass]="'tooltip-style'"
                        src="../../../assets/img/Right_check.svg" alt="Approve Icon" width="18px" height="18px" />
                    </span>
                  </ng-container>
                  <ng-container *ngIf="(tableConfiguration.showDelete) && (path === '/invoices' || path === '/promotions-management')">
                    <span class="add-edit-user"
                      [class.disabled]="data.status === 'Approved' || data.status === 'Rejected' || data.isAssigned === false"
                      (click)="(data.status !== 'Approved' && data.status !== 'Rejected' && data.isAssigned !== false) && onReject(data); $event.stopPropagation()">

                      <img *ngIf="typeof tableConfiguration.showEdit !== 'string'"
                        [matTooltip]="path === '/invoices' ? 'Reject Invoice' : 'Reject Promotion'"
                        matTooltipClass="large-tooltip" [matTooltipClass]="'tooltip-style'"
                        src="../../../assets/img/close.svg" alt="Reject Icon" width="14px" height="14px" />
                    </span>
                  </ng-container>

                  <span class="add-approve" *ngIf="tableConfiguration.isApprove">
                    <span class="icon-wrapper approve-wrapper" [class.disabled]="data.isDisabled"
                      (click)="approveTarget(data)">
                      <i class="fa fa-check approve-icon" matTooltip="Approve Target"
                        [matTooltipClass]="'tooltip-style'"></i>
                    </span>

                    <span class="icon-wrapper reject-wrapper" [class.disabled]="data.isDisabled"
                      (click)="rejectTarget(data)">
                      <i class="fa fa-times reject-icon" matTooltip="Reject Target"
                        [matTooltipClass]="'tooltip-style'"></i>
                    </span>
                  </span>
                  <span *ngIf="tableConfiguration.showStatus && tableConfiguration.changeStatus" class="add-edit">
                    <img *ngIf="data?.is_active" aria-hidden="true" (click)="onStatusClick(data, $event)"
                      [matTooltipClass]="'tooltip-style'" height="30px" width="30px"
                      src="../../../assets/img/ActiveToggle.svg" alt="" matTooltip="Deactivate" />
                    <img *ngIf="!data?.is_active" aria-hidden="true" matTooltip="Activate"
                      (click)="onStatusClick(data, $event)" height="30px" width="30px"
                      [matTooltipClass]="'tooltip-style'" src="../../../assets/img/deactive_icon.svg" alt="" />
                  </span>
                  <span *ngIf="tableConfiguration.showUpload && activeTabTitle === 'Leaders'" class="add-edit-user"
                    (click)="onUploadClick(data)">
                    <img src="../../../assets/img/Agreement.png" alt="Agreement Icon" width="20px" height="20px"
                      matTooltip="Upload Agreement" [matTooltipClass]="'tooltip-style'" />
                  </span>
                  <span *ngIf="tableConfiguration.showDownload" title="Download" class="add-edit-user"
                    (click)="onDownloadClick(data)">
                    <img src="../../../assets/img/Download_btn.svg" alt="Download Icon" width="20px" height="20px" />
                  </span>

                  <span *ngIf="tableConfiguration.showDelete && path !== '/invoices' && path !== '/promotions-management'" title="Delete"
                    class="add-edit-user" (click)="handleDeleteClick(data)">
                    <img src="../../../assets/img/delete.svg" alt="Delete Icon" width="20px" height="20px" />
                  </span>
                  <span *ngIf="tableConfiguration.editBox && i == showIndex.index" title="Cancel"
                    (click)="updateFalse()" class="add-edit">
                    <i class="fa fa-close close-icon"></i>
                  </span>
                  <span *ngIf="tableConfiguration.editBox && i == showIndex.index" title="Update"
                    (click)="updateRowData()" class="add-edit">
                    <i class="fa fa-check-square-o update-icon"></i>
                  </span>
                  <span class="add-approve" *ngIf="path == '/approver-management'" (click)="handleDeleteClick(data)">
                    <img src="../../../assets/img/delete.svg" alt="Delete" matTooltip="Delete Activity" width="20px"
                      height="20px" class="status-icon" [matTooltipClass]="'tooltip-style'">
                  </span>
                  <ng-container
                    *ngIf="tableConfiguration.showTarget && activeTabTitle === 'Leaders' && path === '/users'">
                    <span  [matTooltip]="(data?.targetStatus === 'NOT_SET' ? '' :  data?.targetStatus === 'PENDING' ? ' Approval Pending' : data?.targetStatus | titlecase)"
                    matTooltipClass="large-tooltip" [matTooltipClass]="'tooltip-style'">
                    <span [class.disabled]="data?.targetStatus !== 'NOT_SET'"
                      (click)="data?.targetStatus === 'NOT_SET' && onShowTargetClick(data, $event)"
                      class="add-edit-user" [ngStyle]="{
                        'opacity': data?.targetStatus === 'NOT_SET' ? '1' : '0.5',
                        'cursor': data?.targetStatus === 'NOT_SET' ? 'pointer' : 'not-allowed'
                      }">
                      <img *ngIf="typeof tableConfiguration.showEdit !== 'string'"
                        [matTooltip]="'Target Status: ' + (data?.targetStatus === 'NOT_SET' ? 'Not Set' : data?.targetStatus === 'PENDING' ? ' Approval Pending' :  data?.targetStatus | titlecase )"
                        matTooltipClass="large-tooltip" [matTooltipClass]="'tooltip-style'"
                        src="../../../assets/img/focus.svg" alt="Upload Icon" width="23px" height="23px" />
                    </span>
                  </span>
                  </ng-container>
                </td>
              </tr>
            </ng-container>
          </ng-container>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination Controls -->
  <div class="pagination-direction" *ngIf="tableConfiguration?.totalRecordCount && tableConfiguration?.showPagination">
    <div *ngFor="let data of tableData | paginate : {
        itemsPerPage: tableConfiguration?.perPage,
        currentPage: tableConfiguration?.currentPage,
        id: 'pagination',
        totalItems: tableConfiguration?.totalRecordCount
      }">
    </div>
    <!-- <div class="total-count" *ngIf="tableConfiguration?.totalRecordCount">
      Total: {{tableConfiguration?.totalRecordCount}}
    </div> -->
    <pagination-controls class="addQnhand" (pageChange)="getPageData($event)" id="pagination" maxSize:any="5"
      autoHide:any="true" directionLinks:any="true" autoHide:any="true" previousLabel="Previous" nextLabel="Next">
    </pagination-controls>
  </div>
</div>
<ng-template #test>
  <div class="change-status">
    <div class="change-status-inner-section">
      <div class="image-container">
        <img src="../../../assets/img/target-dailog.svg" alt="dialog" />
      </div>
      <div class="status-fields">
        <span class="text-fields">
          {{ "Are you sure you want to" }}{{ " " }}
          <span *ngIf="isActive">
            <span *ngIf="path.includes('/brands')">{{ "inactive" }}</span>
            <span *ngIf="path.includes('/financial-review')">{{ "deactivate" }}</span>
            <span *ngIf="!path.includes('/brands') && !path.includes('/financial-review')">{{ "deactivate" }}</span>
          </span>
          <span *ngIf="!isActive">{{ "activate" }}</span> <br />
          <span *ngIf="path.includes('/product-catalog') || path.includes('/brands')">
            {{ "product" }}
          </span>
          <span *ngIf="path.includes('/financial-review') && activeTabTitle === 'Finance Review'">
            {{ "finance review" }}
          </span>
          <span *ngIf="path.includes('/financial-review') && activeTabTitle === 'Exception'">
            {{ "exception" }}
          </span>
          <span *ngIf="!path.includes('/product-catalog') && !path.includes('/brands') && !path.includes('/financial-review')">
            {{ activeTabTitle }}{{ " account" }}
          </span>
          ?
        </span>
      </div>
      <div class="change-status-action-section">
        <button #statusConfirm class="action-btn submit-status" (click)="onConfirmStatusChange()" value="Yes">
          {{ "Yes" }}
        </button>
        <button [mat-dialog-close] class="action-btn cancel-status" value="No">
          {{ "No" }}
        </button>
      </div>
    </div>
  </div>
</ng-template>

<app-target-modal [isOpen]="targetModalOpen" [modalType]="targetModalMode" [defaultValues]="targetDefaults"
  (close)="targetModalOpen = false" (submit)="handleTargetSubmit($event)"
  (refreshUserList)="getPageData(tableConfiguration.currentPage)">
</app-target-modal>

<app-view-documents-modal *ngIf="isViewPDFModalOpen" [documents]="selectedDocuments"
  (close)="isViewPDFModalOpen = false"  (viewDocument)="handleViewDocument($event)" >
</app-view-documents-modal>

<div *ngIf="iframeUrl" class="iframe-backdrop" (click)="handleCloseIframe()"></div>
<div *ngIf="iframeUrl" class="iframe-container">
  <div class="iframe-header">
    <h4>{{ getDocumentTitle() }}</h4>
    <button class="close-iframe" (click)="handleCloseIframe()">&times;</button>
  </div>
  
  <ng-container [ngSwitch]="getDocumentFileType()">
    <iframe *ngSwitchCase="'pdf'"
      [src]="getSafeUrl(iframeUrl)" 
      class="document-frame">
    </iframe>
    
    <div *ngSwitchCase="'image'" class="image-container">
      <img [src]="getSafeUrl(iframeUrl)" 
           alt="Document" 
           class="document-image">
    </div>
    
    <iframe *ngSwitchDefault
      [src]="getSafeUrl(iframeUrl)" 
      class="document-frame">
    </iframe>
  </ng-container>
</div>


<ng-template #approveConfirmDialog>
  <div class="confirm-dialog">
    <div class="image-container">
      <img src="../../../assets/img/target-dailog.svg" alt="dialog" />
    </div>
    <div class="confirm-content">
      <span class="text-fields">
        Are you sure you want to approve this target?
      </span>
    </div>
    <div class="action-button-section">
      <button class="action-btn submit-btn" (click)="approveTarget(selectedData)">
        Yes
      </button>
      <button class="action-btn cancel-btn" (click)="dialog.closeAll()">
        No
      </button>
    </div>
  </div>
</ng-template>
